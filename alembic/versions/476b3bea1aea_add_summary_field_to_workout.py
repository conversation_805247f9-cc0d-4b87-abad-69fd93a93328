"""add_summary_field_to_workout

Revision ID: 476b3bea1aea
Revises: add_user_id_to_workout
Create Date: 2025-06-01 14:15:05.465789

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '476b3bea1aea'
down_revision = 'add_user_id_to_workout'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('water_rni')
    op.drop_table('nutrition_reference_main')
    op.drop_table('mineral_pregnancy_inc')
    op.drop_table('mineral_rni')
    op.drop_table('vitamin_rni')
    op.drop_table('other_dietary_spl_ul')
    op.drop_table('nutrition_pregnancy_inc')
    op.drop_table('water_pregnancy_inc')
    op.drop_table('vitamin_pregnancy_inc')
    op.drop_index('idx_workout_exercises_template_id', table_name='workout_exercises')
    op.drop_index('ix_training_templates_id', table_name='workout_templates')
    op.drop_index('ix_training_templates_user_id', table_name='workout_templates')
    op.create_index(op.f('ix_workout_templates_id'), 'workout_templates', ['id'], unique=False)
    op.create_index(op.f('ix_workout_templates_user_id'), 'workout_templates', ['user_id'], unique=False)
    op.drop_column('workout_templates', 'exercises')
    op.add_column('workouts', sa.Column('summary', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('workouts', 'summary')
    op.add_column('workout_templates', sa.Column('exercises', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_workout_templates_user_id'), table_name='workout_templates')
    op.drop_index(op.f('ix_workout_templates_id'), table_name='workout_templates')
    op.create_index('ix_training_templates_user_id', 'workout_templates', ['user_id'], unique=False)
    op.create_index('ix_training_templates_id', 'workout_templates', ['id'], unique=False)
    op.create_index('idx_workout_exercises_template_id', 'workout_exercises', ['template_id'], unique=False)
    op.create_table('vitamin_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('vitamin_d_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('thiamine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('lactoflavin_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('notes', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='vitamin_pregnancy_inc_pkey')
    )
    op.create_table('water_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('drinking_inc_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_water_inc_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='water_pregnancy_inc_pkey')
    )
    op.create_table('nutrition_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('saturated_fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('saturated_fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pufa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pufa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('n3fa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('n3fa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('la', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('la_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('ala', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ala_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('epa_dha', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('epa_dha_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fiber_dietary', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fiber_dietary_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fructose', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fructose_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_rni', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_rni_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='nutrition_pregnancy_inc_pkey')
    )
    op.create_table('other_dietary_spl_ul',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name_cn', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('spl', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('spl_unit', sa.CHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('ul', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ul_unit', sa.CHAR(length=10), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='other_dietary_spl_ul_pkey')
    )
    op.create_table('vitamin_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('rec_type', sa.VARCHAR(length=3), autoincrement=False, nullable=False),
    sa.Column('vitamin_a', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_a_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_d', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_d_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_e', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_e_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('thiamine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('thiamine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('lactoflavin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('lactoflavin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_b6', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_b6_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_b12', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_b12_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_c', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_c_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('niacin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('niacin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('folacin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('folacin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pantothenic', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pantothenic_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('biotin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('biotin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('choline', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('choline_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='vitamin_rni_pkey')
    )
    op.create_table('mineral_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('rec_type', sa.VARCHAR(length=3), autoincrement=False, nullable=False),
    sa.Column('calcium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('calcium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('phosphor', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('phosphor_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('kalium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('kalium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('natrium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('natrium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('magnesium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('magnesium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('chlorine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chlorine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('iron', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iron_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('iodine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iodine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('zinc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('zinc_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('selenium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('selenium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('copper', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('copper_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fluorine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fluorine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('chromium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chromium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('cobalt', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('cobalt_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('manganese', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('manganese_unit', sa.CHAR(length=10), server_default=sa.text("'mg'::bpchar"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='mineral_rni_pkey')
    )
    op.create_table('mineral_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('calcium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('phosphor_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('kalium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('natrium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('magnesium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chlorine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iron_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iodine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('zinc_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('selenium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('copper_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fluorine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chromium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('cobalt_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='mineral_pregnancy_inc_pkey')
    )
    op.create_table('nutrition_reference_main',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('saturated_fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('saturated_fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pufa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pufa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('n3fa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('n3fa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('la', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('la_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('ala', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ala_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('epa_dha', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('epa_dha_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fiber_dietary', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fiber_dietary_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fructose', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fructose_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_rni', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_rni_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='nutrition_reference_main_pkey')
    )
    op.create_table('water_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('drinking_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_water_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='water_rni_pkey')
    )
    # ### end Alembic commands ### 