"""add template_source field to workouts table

Revision ID: add_template_source
Revises: add_visibility_field
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_template_source'
down_revision = 'add_visibility_field'  # 设置为最新的迁移ID
branch_labels = None
depends_on = None


def upgrade():
    """添加template_source字段到workouts表"""
    # 添加template_source字段
    op.add_column('workouts', sa.Column('template_source', postgresql.JSON(astext_type=sa.Text()), nullable=True))


def downgrade():
    """移除template_source字段"""
    # 移除template_source字段
    op.drop_column('workouts', 'template_source') 