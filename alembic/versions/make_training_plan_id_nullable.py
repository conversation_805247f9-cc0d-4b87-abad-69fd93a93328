"""make training_plan_id nullable in workouts table

Revision ID: make_training_plan_id_nullable
Revises: add_template_source
Create Date: 2024-12-19 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'make_training_plan_id_nullable'
down_revision = 'add_template_source'
branch_labels = None
depends_on = None


def upgrade():
    """将training_plan_id字段设置为可空"""
    # 修改 training_plan_id 字段为可空
    op.alter_column('workouts', 'training_plan_id', 
                   existing_type=sa.Integer(), 
                   nullable=True)


def downgrade():
    """回滚：将training_plan_id字段设置为不可空"""
    # 注意：在回滚之前，需要确保所有的training_plan_id都不为NULL
    # 否则这个操作会失败
    op.alter_column('workouts', 'training_plan_id', 
                   existing_type=sa.Integer(), 
                   nullable=False) 