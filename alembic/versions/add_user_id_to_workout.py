"""add user_id to workouts table

Revision ID: add_user_id_to_workout
Revises: make_training_plan_id_nullable
Create Date: 2024-12-19 13:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_user_id_to_workout'
down_revision = 'make_training_plan_id_nullable'
branch_labels = None
depends_on = None


def upgrade():
    """添加user_id字段到workouts表"""
    # 添加user_id字段
    op.add_column('workouts', sa.Column('user_id', sa.Integer(), nullable=True))
    
    # 添加外键约束
    op.create_foreign_key(
        'fk_workouts_user_id', 
        'workouts', 
        'users', 
        ['user_id'], 
        ['id']
    )
    
    # 添加索引
    op.create_index('ix_workouts_user_id', 'workouts', ['user_id'])


def downgrade():
    """移除user_id字段"""
    # 删除索引
    op.drop_index('ix_workouts_user_id', 'workouts')
    
    # 删除外键约束
    op.drop_constraint('fk_workouts_user_id', 'workouts', type_='foreignkey')
    
    # 删除字段
    op.drop_column('workouts', 'user_id') 