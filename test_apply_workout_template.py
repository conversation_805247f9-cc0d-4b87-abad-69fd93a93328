"""
测试 apply_workout_template 功能的脚本
"""

import asyncio
import json
from datetime import datetime, date
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy import create_engine

# 导入相关模型和服务
from app.core.config import settings
from app.models.training_template import WorkoutTemplate
from app.models.workout import Workout
from app.models.workout_exercise import WorkoutExercise
from app.models.set_record import SetRecord
from app.models.user import User
from app.services.training_template_service import TrainingTemplateService


def test_apply_workout_template():
    """测试应用训练模板功能"""
    
    # 创建数据库连接
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 1. 获取一个用户和模板进行测试
        user = db.query(User).first()
        if not user:
            print("❌ 没有找到用户，无法进行测试")
            return
        
        template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.user_id == user.id
        ).first()
        
        if not template:
            print("❌ 没有找到训练模板，无法进行测试")
            return
        
        print(f"✅ 找到用户: {user.username} (ID: {user.id})")
        print(f"✅ 找到模板: {template.name} (ID: {template.id})")
        
        # 2. 测试应用模板
        template_service = TrainingTemplateService(db)
        target_date = datetime(2024, 12, 20)  # 测试日期
        
        print(f"\n🔄 开始应用模板到日期: {target_date.strftime('%Y-%m-%d')}")
        
        result = template_service.apply_workout_template(
            template_id=template.id,
            user_id=user.id,
            scheduled_date=target_date,
            training_plan_id=None  # 暂时不关联训练计划
        )
        
        if result:
            print("✅ 成功应用训练模板!")
            print(f"📝 创建的训练日ID: {result['workout_id']}")
            print(f"📝 训练日名称: {result['workout']['name']}")
            print(f"📝 训练日期: {result['workout']['scheduled_date']}")
            print(f"📝 包含运动数量: {result['workout']['exercise_count']}")
            print(f"📝 模板来源信息: {json.dumps(result['workout']['template_source'], indent=2, ensure_ascii=False)}")
            
            # 3. 验证创建的记录
            created_workout = db.query(Workout).filter(
                Workout.id == result['workout_id']
            ).first()
            
            if created_workout:
                print(f"\n✅ 验证训练日记录:")
                print(f"   - ID: {created_workout.id}")
                print(f"   - 名称: {created_workout.name}")
                print(f"   - 状态: {created_workout.status}")
                print(f"   - 计划日期: {created_workout.scheduled_date}")
                print(f"   - 目标部位: {created_workout.target_body_parts}")
                print(f"   - 训练场景: {created_workout.training_scenario}")
                print(f"   - 模板来源: {created_workout.template_source}")
                
                # 4. 验证训练动作记录
                exercises = db.query(WorkoutExercise).filter(
                    WorkoutExercise.workout_id == created_workout.id
                ).all()
                
                print(f"\n✅ 验证训练动作记录 (共 {len(exercises)} 个):")
                for ex in exercises:
                    print(f"   - 动作ID: {ex.exercise_id}, 组数: {ex.sets}, 次数: {ex.reps}, 重量: {ex.weight}")
                    
                    # 5. 验证组记录
                    set_records = db.query(SetRecord).filter(
                        SetRecord.workout_exercise_id == ex.id
                    ).all()
                    
                    print(f"     组记录 (共 {len(set_records)} 个):")
                    for sr in set_records:
                        print(f"       第{sr.set_number}组: {sr.weight}kg x {sr.reps}次, 完成: {sr.completed}")
                
                print("\n🎉 所有验证通过!")
                
        else:
            print("❌ 应用训练模板失败")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


def test_get_workouts_from_template():
    """测试获取从模板创建的训练日记录"""
    
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 获取用户和模板
        user = db.query(User).first()
        template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.user_id == user.id
        ).first()
        
        if not user or not template:
            print("❌ 没有找到用户或模板")
            return
        
        # 获取从模板创建的训练日
        from app.crud.crud_workout import CRUDWorkout
        workout_crud = CRUDWorkout(db)
        workouts = workout_crud.get_workouts_from_template(template.id, user.id)
        
        print(f"✅ 从模板 '{template.name}' 创建的训练日记录 (共 {len(workouts)} 个):")
        for workout in workouts:
            print(f"   - ID: {workout.id}, 名称: {workout.name}")
            print(f"     日期: {workout.scheduled_date}, 状态: {workout.status}")
            print(f"     模板来源: {workout.template_source.get('template_name') if workout.template_source else '无'}")
    
    except Exception as e:
        print(f"❌ 测试获取模板训练日失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


if __name__ == "__main__":
    print("🧪 开始测试 apply_workout_template 功能\n")
    
    # 测试应用模板
    test_apply_workout_template()
    
    print("\n" + "="*50 + "\n")
    
    # 测试获取从模板创建的训练日
    test_get_workouts_from_template()
    
    print("\n🎯 测试完成!") 