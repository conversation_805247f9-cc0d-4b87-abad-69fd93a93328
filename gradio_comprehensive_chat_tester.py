#!/usr/bin/env python3
"""
全面的Gradio聊天测试系统
测试完整的聊天功能，包括历史记录、流式响应、消息入库等
"""

import gradio as gr
import requests
import json
import time
import asyncio
import threading
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveChatTester:
    """全面聊天测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        self.chat_api_base = f"{base_url}/api/v2"
        self.access_token = None
        self.user_info = {}
        self.conversation_id = None
        self.session_id = str(uuid.uuid4())
        self.message_history = []
        
    def authenticate(self, email: str, password: str) -> Tuple[bool, str]:
        """用户认证"""
        try:
            response = requests.post(
                f"{self.api_base}/auth/login",
                json={"email": email, "password": password},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                self.user_info = {"id": data.get("user_id"), "email": email}
                return True, f"✅ 登录成功，用户ID: {self.user_info['id']}"
            else:
                return False, f"❌ 登录失败: {response.text}"
                
        except Exception as e:
            return False, f"❌ 登录异常: {str(e)}"
    
    def get_conversation_history(self) -> Tuple[bool, str, List[Dict]]:
        """获取对话历史"""
        if not self.access_token:
            return False, "❌ 未登录", []
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            # 获取对话列表
            response = requests.get(
                f"{self.chat_api_base}/chat/conversations",
                headers=headers
            )
            
            if response.status_code == 200:
                conversations = response.json()
                if conversations:
                    # 使用第一个对话
                    self.conversation_id = conversations[0].get("id")
                    
                    # 获取消息历史
                    msg_response = requests.get(
                        f"{self.chat_api_base}/chat/conversations/{self.conversation_id}/messages",
                        headers=headers
                    )
                    
                    if msg_response.status_code == 200:
                        messages = msg_response.json()
                        self.message_history = messages
                        return True, f"✅ 加载了 {len(messages)} 条历史消息", messages
                    else:
                        return False, f"❌ 获取消息失败: {msg_response.text}", []
                else:
                    return True, "ℹ️ 暂无对话历史", []
            else:
                return False, f"❌ 获取对话失败: {response.text}", []
                
        except Exception as e:
            return False, f"❌ 获取历史异常: {str(e)}", []
    
    def send_message(self, message: str, use_streaming: bool = True) -> Tuple[bool, str, Dict]:
        """发送消息"""
        if not self.access_token:
            return False, "❌ 未登录", {}
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "message": message,
                "session_id": self.session_id,
                "conversation_id": self.conversation_id,
                "stream": use_streaming
            }
            
            start_time = time.time()
            
            if use_streaming:
                # 流式响应
                response = requests.post(
                    f"{self.chat_api_base}/chat/stream",
                    json=payload,
                    headers=headers,
                    stream=True
                )
                
                if response.status_code == 200:
                    full_response = ""
                    chunks = []
                    
                    for line in response.iter_lines():
                        if line:
                            try:
                                line_text = line.decode('utf-8')
                                if line_text.startswith('data: '):
                                    data_text = line_text[6:]
                                    if data_text.strip() == '[DONE]':
                                        break
                                    
                                    chunk_data = json.loads(data_text)
                                    chunk_content = chunk_data.get('content', '')
                                    full_response += chunk_content
                                    chunks.append(chunk_data)
                                    
                            except json.JSONDecodeError:
                                continue
                    
                    end_time = time.time()
                    
                    result = {
                        "response": full_response,
                        "chunks_count": len(chunks),
                        "response_time": end_time - start_time,
                        "streaming": True,
                        "conversation_id": self.conversation_id
                    }
                    
                    return True, f"✅ 流式响应完成 ({len(chunks)} 块)", result
                else:
                    return False, f"❌ 流式请求失败: {response.text}", {}
            else:
                # 普通响应
                response = requests.post(
                    f"{self.chat_api_base}/chat/message",
                    json=payload,
                    headers=headers
                )
                
                end_time = time.time()
                
                if response.status_code == 200:
                    data = response.json()
                    result = {
                        "response": data.get("response", ""),
                        "response_time": end_time - start_time,
                        "streaming": False,
                        "conversation_id": data.get("conversation_id"),
                        "message_id": data.get("message_id")
                    }
                    
                    # 更新conversation_id
                    if data.get("conversation_id"):
                        self.conversation_id = data.get("conversation_id")
                    
                    return True, f"✅ 普通响应完成", result
                else:
                    return False, f"❌ 普通请求失败: {response.text}", {}
                    
        except Exception as e:
            return False, f"❌ 发送消息异常: {str(e)}", {}
    
    def test_message_persistence(self) -> Tuple[bool, str]:
        """测试消息持久化"""
        if not self.conversation_id:
            return False, "❌ 无对话ID，无法测试持久化"
        
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            # 重新获取消息历史，检查是否包含刚发送的消息
            response = requests.get(
                f"{self.chat_api_base}/chat/conversations/{self.conversation_id}/messages",
                headers=headers
            )
            
            if response.status_code == 200:
                messages = response.json()
                new_count = len(messages)
                old_count = len(self.message_history)
                
                if new_count > old_count:
                    self.message_history = messages
                    return True, f"✅ 消息已持久化，历史消息数: {old_count} → {new_count}"
                else:
                    return False, f"❌ 消息未持久化，消息数未增加: {new_count}"
            else:
                return False, f"❌ 获取消息失败: {response.text}"
                
        except Exception as e:
            return False, f"❌ 测试持久化异常: {str(e)}"

def create_gradio_interface():
    """创建Gradio界面"""
    tester = ComprehensiveChatTester()
    
    def login_user(email, password):
        """登录用户"""
        success, message = tester.authenticate(email, password)
        if success:
            # 登录成功后加载历史记录
            hist_success, hist_message, history = tester.get_conversation_history()
            full_message = f"{message}\n{hist_message}"
            
            # 格式化历史记录显示
            if history:
                history_text = "📜 **对话历史:**\n\n"
                for msg in history[-5:]:  # 显示最近5条
                    role = "👤 用户" if msg.get("role") == "user" else "🤖 AI"
                    content = msg.get("content", "")[:100] + "..." if len(msg.get("content", "")) > 100 else msg.get("content", "")
                    history_text += f"{role}: {content}\n\n"
                full_message += f"\n\n{history_text}"
            
            return full_message, gr.update(visible=True), gr.update(visible=False)
        else:
            return message, gr.update(visible=False), gr.update(visible=True)
    
    def send_chat_message(message, use_streaming, chat_history):
        """发送聊天消息"""
        if not message.strip():
            return chat_history, "", "请输入消息"
        
        # 添加用户消息到历史
        chat_history.append([message, None])
        
        # 发送消息
        success, status_msg, result = tester.send_message(message, use_streaming)
        
        if success:
            ai_response = result.get("response", "")
            response_time = result.get("response_time", 0)
            
            # 更新聊天历史
            chat_history[-1][1] = ai_response
            
            # 测试消息持久化
            persist_success, persist_msg = tester.test_message_persistence()
            
            status_info = f"✅ 响应时间: {response_time:.2f}s"
            if use_streaming:
                status_info += f" | 流式块数: {result.get('chunks_count', 0)}"
            status_info += f"\n{persist_msg}"
            
            return chat_history, "", status_info
        else:
            chat_history[-1][1] = f"❌ 发送失败: {status_msg}"
            return chat_history, "", status_msg
    
    def load_conversation_history():
        """加载对话历史"""
        success, message, history = tester.get_conversation_history()
        
        if success and history:
            chat_history = []
            for msg in history:
                if msg.get("role") == "user":
                    chat_history.append([msg.get("content", ""), None])
                elif msg.get("role") == "assistant" and chat_history:
                    chat_history[-1][1] = msg.get("content", "")
            
            return chat_history, f"✅ 加载了 {len(history)} 条历史消息"
        else:
            return [], message
    
    def test_streaming_vs_normal():
        """测试流式vs普通响应对比"""
        test_message = "请简单介绍一下健身的好处"
        
        # 测试普通响应
        success1, msg1, result1 = tester.send_message(test_message, use_streaming=False)
        
        # 测试流式响应
        success2, msg2, result2 = tester.send_message(test_message, use_streaming=True)
        
        if success1 and success2:
            comparison = f"""
📊 **响应方式对比测试**

🔹 **普通响应:**
- 响应时间: {result1.get('response_time', 0):.2f}s
- 响应长度: {len(result1.get('response', ''))} 字符

🔹 **流式响应:**
- 响应时间: {result2.get('response_time', 0):.2f}s
- 流式块数: {result2.get('chunks_count', 0)}
- 响应长度: {len(result2.get('response', ''))} 字符

✅ 两种方式都工作正常
"""
        else:
            comparison = f"❌ 测试失败:\n普通响应: {msg1}\n流式响应: {msg2}"
        
        return comparison
    
    # 创建界面
    with gr.Blocks(title="全面聊天功能测试系统", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🧪 全面聊天功能测试系统")
        gr.Markdown("测试完整的聊天功能：认证、历史记录、流式响应、消息持久化等")
        
        with gr.Row():
            with gr.Column(scale=1):
                # 登录区域
                with gr.Group() as login_group:
                    gr.Markdown("## 🔐 用户登录")
                    email_input = gr.Textbox(label="邮箱", value="<EMAIL>")
                    password_input = gr.Textbox(label="密码", type="password", value="testpass123")
                    login_btn = gr.Button("登录", variant="primary")
                    login_status = gr.Textbox(label="登录状态", interactive=False)
                
                # 功能测试区域
                with gr.Group(visible=False) as test_group:
                    gr.Markdown("## 🛠️ 功能测试")
                    
                    load_history_btn = gr.Button("📜 加载对话历史")
                    history_status = gr.Textbox(label="历史加载状态", interactive=False)
                    
                    test_comparison_btn = gr.Button("📊 测试响应方式对比")
                    comparison_result = gr.Textbox(label="对比测试结果", interactive=False, lines=10)
            
            with gr.Column(scale=2):
                # 聊天区域
                with gr.Group(visible=False) as chat_group:
                    gr.Markdown("## 💬 聊天测试")
                    
                    chatbot = gr.Chatbot(height=400, label="聊天记录")
                    
                    with gr.Row():
                        message_input = gr.Textbox(
                            label="输入消息", 
                            placeholder="输入您的消息...",
                            scale=4
                        )
                        use_streaming = gr.Checkbox(label="使用流式响应", value=True)
                    
                    with gr.Row():
                        send_btn = gr.Button("发送", variant="primary")
                        clear_btn = gr.Button("清空")
                    
                    chat_status = gr.Textbox(label="聊天状态", interactive=False)
        
        # 事件绑定
        login_btn.click(
            login_user,
            inputs=[email_input, password_input],
            outputs=[login_status, test_group, login_group]
        )
        
        login_btn.click(
            lambda: gr.update(visible=True),
            outputs=[chat_group]
        )
        
        send_btn.click(
            send_chat_message,
            inputs=[message_input, use_streaming, chatbot],
            outputs=[chatbot, message_input, chat_status]
        )
        
        message_input.submit(
            send_chat_message,
            inputs=[message_input, use_streaming, chatbot],
            outputs=[chatbot, message_input, chat_status]
        )
        
        clear_btn.click(
            lambda: ([], ""),
            outputs=[chatbot, chat_status]
        )
        
        load_history_btn.click(
            load_conversation_history,
            outputs=[chatbot, history_status]
        )
        
        test_comparison_btn.click(
            test_streaming_vs_normal,
            outputs=[comparison_result]
        )
    
    return demo

if __name__ == "__main__":
    print("🚀 启动全面聊天功能测试系统...")
    
    demo = create_gradio_interface()
    
    # 启动Gradio应用
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,  # 使用不同端口避免冲突
        share=False,
        debug=True
    ) 