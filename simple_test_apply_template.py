"""
简化的测试脚本，直接测试 apply_workout_template 功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

# 直接导入需要的模型
from app.core.config import settings
from app.models.training_template import WorkoutTemplate
from app.models.workout import Workout
from app.models.workout_exercise import WorkoutExercise
from app.models.set_record import SetRecord
from app.models.user import User

def test_database_connection():
    """测试数据库连接"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # 测试查询
        user_count = db.query(User).count()
        template_count = db.query(WorkoutTemplate).count()
        workout_count = db.query(Workout).count()
        
        print(f"✅ 数据库连接成功!")
        print(f"📊 用户数量: {user_count}")
        print(f"📊 训练模板数量: {template_count}")
        print(f"📊 训练日数量: {workout_count}")
        
        # 检查是否有template_source字段
        workout = db.query(Workout).first()
        if workout:
            print(f"📊 第一个训练日的template_source: {getattr(workout, 'template_source', '字段不存在')}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def test_template_source_field():
    """测试template_source字段是否存在"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # 创建一个测试Workout对象
        test_workout = Workout(
            name="测试训练日",
            day_number=1,
            status="not_started",
            template_source={
                "template_id": 123,
                "template_name": "测试模板",
                "applied_at": datetime.utcnow().isoformat()
            }
        )
        
        print("✅ template_source字段可以正常设置!")
        print(f"📝 测试数据: {test_workout.template_source}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ template_source字段测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 开始简化测试\n")
    
    # 测试数据库连接
    if test_database_connection():
        print("\n" + "="*50 + "\n")
        
        # 测试template_source字段
        test_template_source_field()
    
    print("\n🎯 测试完成!") 