# 系统流程图和架构图

## 📊 系统整体架构图

### 1. 系统分层架构

```mermaid
graph TB
    subgraph "API接口层"
        A1[REST API]
        A2[WebSocket API]
        A3[GraphQL API]
    end
    
    subgraph "整合服务层"
        B1[IntegratedStateAdapter]
        B2[IntegratedIntentProcessor]
        B3[IntegratedStateManager]
        B4[EnhancedParameterManager]
    end
    
    subgraph "执行引擎层"
        C1[LangGraph Engine]
        C2[Graph Nodes]
        C3[Workflow Orchestrator]
    end
    
    subgraph "智能模块层"
        D1[Advanced AI]
        D2[Learning Engine]
        D3[Optimization]
        D4[Monitoring]
    end
    
    subgraph "数据存储层"
        E1[PostgreSQL]
        E2[Redis Cache]
        E3[Memory Cache]
        E4[File Storage]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C1
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E4
```

### 2. 核心数据流

```mermaid
graph LR
    A[用户请求] --> B[API网关]
    B --> C[状态适配器]
    C --> D[意图处理器]
    D --> E[参数管理器]
    E --> F[LangGraph引擎]
    F --> G[图节点执行]
    G --> H[响应生成]
    H --> I[状态保存]
    I --> J[用户响应]
    
    subgraph "缓存层"
        K[L1内存]
        L[L2 Redis]
        M[L3数据库]
    end
    
    C -.-> K
    D -.-> L
    I -.-> M
```

## 🔄 核心业务流程

### 1. 用户消息处理流程

```mermaid
graph TD
    A[用户消息] --> B{消息预处理}
    B --> C[状态获取]
    C --> D[意图识别]
    D --> E{置信度检查}
    
    E -->|高置信度 >0.8| F[直接路由]
    E -->|低置信度 <0.8| G[智能路由]
    
    F --> H[专家节点]
    G --> I[LLM分析]
    I --> H
    
    H --> J{参数检查}
    J -->|参数完整| K[业务处理]
    J -->|参数缺失| L[参数收集]
    
    L --> M[用户交互]
    M --> N{收集完成}
    N -->|是| K
    N -->|否| L
    
    K --> O[响应生成]
    O --> P[状态保存]
    P --> Q[返回响应]
```

### 2. 状态管理流程

```mermaid
graph TD
    A[状态请求] --> B{缓存检查}
    B -->|命中| C[返回缓存状态]
    B -->|未命中| D[检查点查询]
    
    D --> E{检查点存在}
    E -->|存在| F[恢复状态]
    E -->|不存在| G[原始系统查询]
    
    G --> H{原始状态存在}
    H -->|存在| I[状态迁移]
    H -->|不存在| J[创建新状态]
    
    F --> K[状态适配]
    I --> K
    J --> K
    
    K --> L[缓存更新]
    L --> M[返回状态]
    
    subgraph "状态保存流程"
        N[状态更新] --> O[多目标保存]
        O --> P[LangGraph检查点]
        O --> Q[原始系统]
        O --> R[缓存层]
        
        P --> S{保存成功}
        Q --> S
        R --> S
        S -->|至少一个成功| T[保存完成]
        S -->|全部失败| U[保存失败]
    end
```

### 3. 意图处理流程

```mermaid
graph TD
    A[用户消息] --> B[预处理]
    B --> C[快速规则匹配]
    C --> D{规则匹配成功}
    
    D -->|成功| E[规则意图]
    D -->|失败| F[上下文构建]
    
    F --> G[LLM意图识别]
    G --> H[置信度计算]
    H --> I{置信度评估}
    
    I -->|高置信度| J[确定意图]
    I -->|低置信度| K[多模型验证]
    
    K --> L[投票决策]
    L --> M[最终意图]
    
    E --> N[意图路由]
    J --> N
    M --> N
    
    N --> O{路由决策}
    O -->|训练计划| P[训练专家]
    O -->|运动推荐| Q[运动专家]
    O -->|健身问答| R[问答专家]
    O -->|参数收集| S[参数收集器]
```

## 🎯 LangGraph执行流程

### 1. 图构建和执行

```mermaid
graph TD
    A[图初始化] --> B[节点注册]
    B --> C[边定义]
    C --> D[条件路由设置]
    D --> E[检查点配置]
    E --> F[图编译]
    
    F --> G[执行开始]
    G --> H[入口节点]
    H --> I[节点执行]
    I --> J{条件检查}
    
    J -->|条件A| K[节点A]
    J -->|条件B| L[节点B]
    J -->|条件C| M[节点C]
    
    K --> N[状态更新]
    L --> N
    M --> N
    
    N --> O{继续执行}
    O -->|是| P[下一节点]
    O -->|否| Q[执行结束]
    
    P --> I
    Q --> R[结果返回]
    
    subgraph "错误处理"
        S[节点异常] --> T[错误分析]
        T --> U{可重试}
        U -->|是| V[重试执行]
        U -->|否| W[错误恢复]
        V --> I
        W --> X[降级处理]
    end
```

### 2. 并行节点执行

```mermaid
graph TD
    A[并行入口] --> B[任务分发]
    B --> C[节点1]
    B --> D[节点2]
    B --> E[节点3]
    
    C --> F[结果1]
    D --> G[结果2]
    E --> H[结果3]
    
    F --> I[结果聚合]
    G --> I
    H --> I
    
    I --> J[结果选择]
    J --> K{选择策略}
    
    K -->|最佳结果| L[选择最优]
    K -->|投票决策| M[多数投票]
    K -->|加权平均| N[加权结果]
    
    L --> O[最终结果]
    M --> O
    N --> O
```

## 🔧 参数收集流程

### 1. 智能参数收集

```mermaid
graph TD
    A[参数收集开始] --> B[分析当前状态]
    B --> C[识别缺失参数]
    C --> D{参数类型分析}
    
    D -->|基础信息| E[用户基础信息收集]
    D -->|训练参数| F[训练参数收集]
    D -->|偏好设置| G[偏好参数收集]
    
    E --> H[年龄性别收集]
    E --> I[身高体重收集]
    
    F --> J[健身目标收集]
    F --> K[运动经验收集]
    F --> L[时间频率收集]
    
    G --> M[运动偏好收集]
    G --> N[设备偏好收集]
    
    H --> O[参数验证]
    I --> O
    J --> O
    K --> O
    L --> O
    M --> O
    N --> O
    
    O --> P{验证结果}
    P -->|通过| Q[参数保存]
    P -->|失败| R[重新收集]
    
    R --> S[错误提示]
    S --> T[引导重新输入]
    T --> O
    
    Q --> U[收集完成]
```

### 2. 渐进式参数收集

```mermaid
graph TD
    A[开始收集] --> B[参数优先级排序]
    B --> C[选择最重要参数]
    C --> D[单参数收集]
    D --> E[用户友好提示]
    E --> F[等待用户输入]
    F --> G[输入验证]
    G --> H{验证通过}
    
    H -->|是| I[参数保存]
    H -->|否| J[错误提示]
    J --> E
    
    I --> K[更新收集状态]
    K --> L{还有参数}
    L -->|是| M[选择下一参数]
    L -->|否| N[收集完成]
    
    M --> C
    N --> O[参数完整性检查]
    O --> P{检查结果}
    P -->|完整| Q[收集成功]
    P -->|不完整| R[补充收集]
    R --> B
```

## 🚨 错误处理和恢复流程

### 1. 统一错误处理

```mermaid
graph TD
    A[错误发生] --> B[错误捕获]
    B --> C[错误分析]
    C --> D[错误分类]
    
    D --> E{错误类型}
    E -->|LLM错误| F[LLM错误处理]
    E -->|数据库错误| G[数据库错误处理]
    E -->|网络错误| H[网络错误处理]
    E -->|验证错误| I[验证错误处理]
    E -->|业务错误| J[业务错误处理]
    
    F --> K[严重性评估]
    G --> K
    H --> K
    I --> K
    J --> K
    
    K --> L{严重性级别}
    L -->|低| M[记录日志]
    L -->|中| N[错误恢复]
    L -->|高| O[降级处理]
    L -->|严重| P[熔断处理]
    
    M --> Q[继续执行]
    N --> R[重试机制]
    O --> S[备用方案]
    P --> T[系统保护]
    
    R --> U{重试成功}
    U -->|是| Q
    U -->|否| V[重试次数检查]
    V --> W{达到上限}
    W -->|是| O
    W -->|否| R
```

### 2. 智能重试机制

```mermaid
graph TD
    A[重试触发] --> B[错误类型分析]
    B --> C{可重试判断}
    C -->|不可重试| D[直接失败]
    C -->|可重试| E[重试策略选择]
    
    E --> F{策略类型}
    F -->|固定延迟| G[固定间隔重试]
    F -->|指数退避| H[指数延迟重试]
    F -->|线性退避| I[线性延迟重试]
    F -->|抖动退避| J[随机抖动重试]
    F -->|自适应| K[智能延迟重试]
    
    G --> L[执行重试]
    H --> L
    I --> L
    J --> L
    K --> L
    
    L --> M{重试结果}
    M -->|成功| N[重试成功]
    M -->|失败| O[重试次数检查]
    
    O --> P{达到上限}
    P -->|否| Q[计算下次延迟]
    P -->|是| R[重试失败]
    
    Q --> S[等待延迟]
    S --> L
```

## 📊 性能监控流程

### 1. 实时性能监控

```mermaid
graph TD
    A[性能数据收集] --> B[指标分类]
    B --> C[响应时间监控]
    B --> D[吞吐量监控]
    B --> E[错误率监控]
    B --> F[资源使用监控]
    
    C --> G[时间序列存储]
    D --> G
    E --> G
    F --> G
    
    G --> H[实时分析]
    H --> I[阈值检查]
    I --> J{超过阈值}
    
    J -->|是| K[告警触发]
    J -->|否| L[正常监控]
    
    K --> M[告警分级]
    M --> N{告警级别}
    N -->|警告| O[警告通知]
    N -->|严重| P[严重告警]
    N -->|紧急| Q[紧急处理]
    
    L --> R[数据存储]
    O --> R
    P --> S[自动处理]
    Q --> T[人工介入]
    
    S --> U[处理结果]
    T --> U
    U --> V[监控恢复]
```

### 2. 自动性能优化

```mermaid
graph TD
    A[性能数据分析] --> B[瓶颈识别]
    B --> C[优化策略生成]
    C --> D{优化类型}
    
    D -->|缓存优化| E[缓存策略调整]
    D -->|资源优化| F[资源分配调整]
    D -->|算法优化| G[算法参数调整]
    D -->|架构优化| H[架构调整建议]
    
    E --> I[自动执行]
    F --> I
    G --> I
    H --> J[人工确认]
    
    I --> K[效果监控]
    J --> L[手动执行]
    L --> K
    
    K --> M[优化效果评估]
    M --> N{效果评估}
    N -->|有效| O[保持优化]
    N -->|无效| P[回滚优化]
    N -->|负面| Q[紧急回滚]
    
    O --> R[优化记录]
    P --> S[策略调整]
    Q --> T[系统恢复]
    
    S --> C
```

这些流程图展示了智能健身AI助手系统的核心业务流程、技术架构和关键功能的执行逻辑，为系统的理解、维护和优化提供了清晰的可视化指导。
