# 统一聊天API增强实现规范

## 概述

统一聊天API增强旨在整合所有优化组件，提供统一的API接口，支持REST/流式/WebSocket多种通信方式，实现智能缓存集成、后台处理队列和全面的健康检查。

## 架构设计

### 统一API架构

```python
class UnifiedChatAPI:
    """统一聊天API主类"""
    
    def __init__(self, db: Session):
        self.db = db
        
        # 核心组件集成
        self.enhanced_langgraph = EnhancedLangGraphService(db)
        self.websocket_optimizer = WebSocketPerformanceOptimizer()
        self.cache_optimizer = AdvancedCacheOptimizer(db)
        self.parameter_collector = ProgressiveParameterCollector(db)
        
        # API处理器
        self.rest_handler = RESTChatHandler(self)
        self.streaming_handler = StreamingChatHandler(self)
        self.websocket_handler = WebSocketChatHandler(self)
        
        # 后台处理
        self.background_processor = BackgroundTaskProcessor()
        self.health_monitor = APIHealthMonitor()
        
    async def initialize(self):
        """初始化统一API"""
        await self.enhanced_langgraph.initialize()
        await self.websocket_optimizer.initialize()
        await self.cache_optimizer.initialize()
        
        # 启动后台任务
        await self.background_processor.start()
        await self.health_monitor.start()
        
        logger.info("统一聊天API初始化完成")
```

### REST API处理器

```python
class RESTChatHandler:
    """REST API处理器"""
    
    def __init__(self, unified_api: UnifiedChatAPI):
        self.unified_api = unified_api
        self.rate_limiter = RateLimiter()
        self.request_validator = RequestValidator()
    
    async def handle_chat_request(self, request: ChatRequest, 
                                 current_user: User) -> ChatResponse:
        """处理聊天请求"""
        # 请求验证
        await self.request_validator.validate(request)
        
        # 速率限制检查
        await self.rate_limiter.check_limit(current_user.id)
        
        # 缓存检查
        cache_key = self._generate_cache_key(request, current_user.id)
        cached_response = await self.unified_api.cache_optimizer.get(cache_key)
        
        if cached_response:
            return cached_response
        
        # 处理请求
        response = await self._process_chat_request(request, current_user)
        
        # 缓存响应
        await self.unified_api.cache_optimizer.set(
            cache_key, response, ttl=3600
        )
        
        return response
    
    async def _process_chat_request(self, request: ChatRequest, 
                                   user: User) -> ChatResponse:
        """处理聊天请求核心逻辑"""
        # 构建工作流请求
        workflow_request = WorkflowRequest(
            message=request.message,
            user_id=user.id,
            session_id=request.session_id,
            context=request.context,
            stream_response=False
        )
        
        # 使用增强LangGraph服务处理
        workflow_response = await self.unified_api.enhanced_langgraph.process_request(
            workflow_request
        )
        
        # 转换为API响应格式
        return ChatResponse(
            response=workflow_response.content,
            session_id=request.session_id,
            metadata=workflow_response.metadata,
            processing_time=workflow_response.processing_time
        )
```

### 流式API处理器

```python
class StreamingChatHandler:
    """流式API处理器"""
    
    def __init__(self, unified_api: UnifiedChatAPI):
        self.unified_api = unified_api
        self.stream_manager = StreamManager()
    
    async def handle_streaming_request(self, request: ChatRequest, 
                                     current_user: User) -> AsyncGenerator[Dict[str, Any], None]:
        """处理流式聊天请求"""
        stream_id = f"stream_{current_user.id}_{int(time.time())}"
        
        try:
            # 注册流
            await self.stream_manager.register_stream(stream_id, current_user.id)
            
            # 构建工作流请求
            workflow_request = WorkflowRequest(
                message=request.message,
                user_id=current_user.id,
                session_id=request.session_id,
                context=request.context,
                stream_response=True
            )
            
            # 流式处理
            async for chunk in self.unified_api.enhanced_langgraph.process_request_stream(
                workflow_request
            ):
                # 检查流是否被中断
                if await self.stream_manager.is_interrupted(stream_id):
                    yield {"type": "stream_interrupted", "stream_id": stream_id}
                    break
                
                # 发送数据块
                yield {
                    "type": "chunk",
                    "data": chunk,
                    "stream_id": stream_id,
                    "timestamp": datetime.now().isoformat()
                }
            
            # 流完成
            yield {"type": "stream_complete", "stream_id": stream_id}
            
        except Exception as e:
            yield {
                "type": "error",
                "error": str(e),
                "stream_id": stream_id
            }
        finally:
            # 清理流
            await self.stream_manager.unregister_stream(stream_id)
```

### WebSocket处理器

```python
class WebSocketChatHandler:
    """WebSocket处理器"""
    
    def __init__(self, unified_api: UnifiedChatAPI):
        self.unified_api = unified_api
        self.connection_manager = unified_api.websocket_optimizer.connection_manager
    
    async def handle_websocket_connection(self, websocket: WebSocket, 
                                        session_id: str, user: User):
        """处理WebSocket连接"""
        connection_id = f"ws_{user.id}_{session_id}_{int(time.time())}"
        
        try:
            # 添加连接到优化器
            success = await self.connection_manager.add_connection(
                connection_id, websocket
            )
            
            if not success:
                await websocket.close(code=1013, reason="连接数已达上限")
                return
            
            # 发送连接确认
            await websocket.send_json({
                "event": "connected",
                "connection_id": connection_id,
                "session_id": session_id
            })
            
            # 消息处理循环
            while True:
                try:
                    # 接收消息
                    data = await websocket.receive_json()
                    
                    # 处理消息
                    await self._handle_websocket_message(
                        connection_id, data, user
                    )
                    
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    await websocket.send_json({
                        "event": "error",
                        "error": str(e)
                    })
        
        finally:
            # 清理连接
            await self.connection_manager.remove_connection(connection_id)
    
    async def _handle_websocket_message(self, connection_id: str, 
                                       data: Dict[str, Any], user: User):
        """处理WebSocket消息"""
        message_type = data.get("type", "chat")
        
        if message_type == "chat":
            await self._handle_chat_message(connection_id, data, user)
        elif message_type == "ping":
            await self._handle_ping(connection_id)
        elif message_type == "interrupt":
            await self._handle_interrupt(connection_id, data)
        else:
            logger.warning(f"未知的WebSocket消息类型: {message_type}")
    
    async def _handle_chat_message(self, connection_id: str, 
                                  data: Dict[str, Any], user: User):
        """处理聊天消息"""
        # 构建请求
        workflow_request = WorkflowRequest(
            message=data.get("message", ""),
            user_id=user.id,
            session_id=data.get("session_id", ""),
            context=data.get("context", {}),
            stream_response=True
        )
        
        # 流式处理并发送响应
        async for chunk in self.unified_api.enhanced_langgraph.process_request_stream(
            workflow_request
        ):
            await self.connection_manager.send_message_to_connection(
                connection_id, {
                    "event": "message_chunk",
                    "data": chunk
                }
            )
```

### 后台任务处理器

```python
class BackgroundTaskProcessor:
    """后台任务处理器"""
    
    def __init__(self):
        self.task_queue = asyncio.Queue()
        self.worker_tasks = []
        self.max_workers = 5
        self.running = False
    
    async def start(self):
        """启动后台处理器"""
        self.running = True
        
        # 启动工作线程
        for i in range(self.max_workers):
            task = asyncio.create_task(self._worker(f"worker_{i}"))
            self.worker_tasks.append(task)
        
        logger.info(f"后台任务处理器启动，{self.max_workers}个工作线程")
    
    async def stop(self):
        """停止后台处理器"""
        self.running = False
        
        # 等待所有任务完成
        for task in self.worker_tasks:
            task.cancel()
        
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
    
    async def submit_task(self, task: BackgroundTask):
        """提交后台任务"""
        await self.task_queue.put(task)
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        while self.running:
            try:
                # 获取任务
                task = await asyncio.wait_for(
                    self.task_queue.get(), timeout=1.0
                )
                
                # 执行任务
                await self._execute_task(task, worker_name)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 执行任务失败: {str(e)}")
    
    async def _execute_task(self, task: BackgroundTask, worker_name: str):
        """执行后台任务"""
        start_time = time.time()
        
        try:
            await task.execute()
            execution_time = time.time() - start_time
            
            logger.info(f"任务 {task.task_id} 执行成功，耗时: {execution_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"任务 {task.task_id} 执行失败: {str(e)}")
            
            # 重试逻辑
            if task.can_retry():
                task.increment_retry_count()
                await self.task_queue.put(task)

class BackgroundTask:
    """后台任务基类"""
    
    def __init__(self, task_id: str, task_type: str, payload: Dict[str, Any]):
        self.task_id = task_id
        self.task_type = task_type
        self.payload = payload
        self.retry_count = 0
        self.max_retries = 3
        self.created_at = datetime.now()
    
    async def execute(self):
        """执行任务（子类实现）"""
        raise NotImplementedError
    
    def can_retry(self) -> bool:
        """是否可以重试"""
        return self.retry_count < self.max_retries
    
    def increment_retry_count(self):
        """增加重试次数"""
        self.retry_count += 1
```

### API健康监控器

```python
class APIHealthMonitor:
    """API健康监控器"""
    
    def __init__(self):
        self.health_checks = {
            "database": DatabaseHealthCheck(),
            "redis": RedisHealthCheck(),
            "llm_service": LLMServiceHealthCheck(),
            "websocket": WebSocketHealthCheck()
        }
        self.monitoring_task = None
        self.health_status = {}
    
    async def start(self):
        """启动健康监控"""
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("API健康监控器启动")
    
    async def stop(self):
        """停止健康监控"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
    
    async def _monitoring_loop(self):
        """监控循环"""
        while True:
            try:
                # 执行所有健康检查
                for name, health_check in self.health_checks.items():
                    status = await health_check.check()
                    self.health_status[name] = status
                
                # 检查整体健康状态
                overall_health = self._calculate_overall_health()
                self.health_status["overall"] = overall_health
                
                # 如果有问题，发送告警
                if not overall_health["healthy"]:
                    await self._send_health_alert(overall_health)
                
                # 等待下次检查
                await asyncio.sleep(30)  # 30秒检查一次
                
            except Exception as e:
                logger.error(f"健康检查失败: {str(e)}")
                await asyncio.sleep(10)
    
    async def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        return {
            "timestamp": datetime.now().isoformat(),
            "status": self.health_status,
            "uptime": self._get_uptime(),
            "version": "1.0.0"
        }
    
    def _calculate_overall_health(self) -> Dict[str, Any]:
        """计算整体健康状态"""
        healthy_components = sum(
            1 for status in self.health_status.values()
            if isinstance(status, dict) and status.get("healthy", False)
        )
        total_components = len(self.health_status)
        
        health_percentage = (healthy_components / total_components) * 100 if total_components > 0 else 0
        
        return {
            "healthy": health_percentage >= 80,  # 80%以上组件健康才算整体健康
            "health_percentage": health_percentage,
            "healthy_components": healthy_components,
            "total_components": total_components
        }

class DatabaseHealthCheck:
    """数据库健康检查"""
    
    async def check(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        try:
            # 执行简单查询测试连接
            start_time = time.time()
            # 这里应该执行实际的数据库查询
            response_time = (time.time() - start_time) * 1000
            
            return {
                "healthy": True,
                "response_time_ms": response_time,
                "last_check": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
```

## API端点定义

### REST端点

```python
@router.post("/api/v1/chat", response_model=ChatResponse)
async def chat_endpoint(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    unified_api: UnifiedChatAPI = Depends(get_unified_api)
):
    """统一聊天REST端点"""
    return await unified_api.rest_handler.handle_chat_request(request, current_user)

@router.post("/api/v1/chat/stream")
async def chat_stream_endpoint(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    unified_api: UnifiedChatAPI = Depends(get_unified_api)
):
    """流式聊天端点"""
    return StreamingResponse(
        unified_api.streaming_handler.handle_streaming_request(request, current_user),
        media_type="application/x-ndjson"
    )

@router.websocket("/api/v1/chat/ws/{session_id}")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    session_id: str,
    current_user: User = Depends(get_current_user_websocket),
    unified_api: UnifiedChatAPI = Depends(get_unified_api)
):
    """WebSocket聊天端点"""
    await unified_api.websocket_handler.handle_websocket_connection(
        websocket, session_id, current_user
    )

@router.get("/api/v1/health", response_model=HealthResponse)
async def health_check_endpoint(
    unified_api: UnifiedChatAPI = Depends(get_unified_api)
):
    """健康检查端点"""
    return await unified_api.health_monitor.get_health_status()
```

## 配置示例

```yaml
# unified_chat_api_config.yml
unified_chat_api:
  # REST API配置
  rest_api:
    rate_limiting:
      requests_per_minute: 60
      burst_size: 10
    caching:
      enabled: true
      default_ttl: 3600
    validation:
      max_message_length: 2000
      required_fields: ["message", "session_id"]
  
  # 流式API配置
  streaming_api:
    chunk_size: 50
    stream_timeout: 300
    max_concurrent_streams: 100
  
  # WebSocket配置
  websocket_api:
    max_connections: 1000
    heartbeat_interval: 30
    message_queue_size: 1000
  
  # 后台处理配置
  background_processing:
    max_workers: 5
    task_timeout: 300
    retry_attempts: 3
  
  # 健康监控配置
  health_monitoring:
    check_interval: 30
    alert_threshold: 80
    components:
      - "database"
      - "redis"
      - "llm_service"
      - "websocket"
```

---

*此规范文档将指导统一聊天API增强的完整实现，确保系统提供高性能、高可用的统一API接口。* 