# 渐进式参数收集器实现规范

## 概述

渐进式参数收集器通过智能化的多阶段收集策略，减少用户输入负担，提升用户体验。系统将学习用户行为模式，智能生成个性化问题，实现高效的信息收集。

## 设计目标

### 用户体验目标
- 减少50%的重复询问
- 智能表单预填充准确率>90%
- 用户完成率提升至>85%
- 平均收集时间减少60%

### 技术目标
- 问题生成响应时间<200ms
- 个性化推荐准确率>85%
- 上下文记忆保持>24小时
- 学习算法准确率>80%

## 架构设计

### 核心组件架构

```python
class ProgressiveParameterCollector:
    """渐进式参数收集器主类"""
    
    def __init__(self, db: Session, llm_proxy: LLMProxyService):
        self.db = db
        self.llm_proxy = llm_proxy
        
        # 核心组件
        self.collection_strategy = MultiPhaseCollectionStrategy()
        self.question_generator = IntelligentQuestionGenerator(llm_proxy)
        self.behavior_learner = UserBehaviorLearner()
        self.context_manager = ContextMemoryManager()
        self.recommendation_engine = PersonalizationEngine()
        
        # 配置
        self.config = {
            "max_questions_per_session": 5,
            "collection_phases": ["basic", "detailed", "advanced"],
            "learning_enabled": True,
            "personalization_threshold": 0.8,
            "context_retention_hours": 24,
            "auto_fill_confidence_threshold": 0.9
        }
        
        # 状态管理
        self.collection_sessions: Dict[str, CollectionSession] = {}
        self.user_profiles: Dict[int, UserProfile] = {}
        
    async def start_collection(self, user_id: int, target_intent: str, 
                             context: Dict[str, Any] = None) -> CollectionSession:
        """开始参数收集会话"""
        session_id = f"collection_{user_id}_{int(time.time())}"
        
        # 创建收集会话
        session = CollectionSession(
            session_id=session_id,
            user_id=user_id,
            target_intent=target_intent,
            context=context or {},
            phase="basic",
            collected_params={},
            questions_asked=[],
            start_time=datetime.now()
        )
        
        # 加载用户画像
        user_profile = await self._load_user_profile(user_id)
        session.user_profile = user_profile
        
        # 分析已有信息
        await self._analyze_existing_information(session)
        
        # 生成初始问题
        first_question = await self.question_generator.generate_next_question(session)
        session.current_question = first_question
        
        self.collection_sessions[session_id] = session
        return session
```

### 多阶段收集策略

```python
class MultiPhaseCollectionStrategy:
    """多阶段收集策略"""
    
    def __init__(self):
        self.phases = {
            "basic": BasicCollectionPhase(),
            "detailed": DetailedCollectionPhase(),
            "advanced": AdvancedCollectionPhase()
        }
        
        # 阶段转换规则
        self.transition_rules = {
            "basic_to_detailed": BasicToDetailedRule(),
            "detailed_to_advanced": DetailedToAdvancedRule(),
            "completion": CompletionRule()
        }
    
    async def determine_next_phase(self, session: CollectionSession) -> str:
        """确定下一个收集阶段"""
        current_phase = session.phase
        
        # 检查当前阶段完成度
        completion_rate = await self._calculate_phase_completion(session)
        
        if completion_rate >= 0.8:  # 当前阶段80%完成
            # 检查是否可以进入下一阶段
            if current_phase == "basic":
                if await self.transition_rules["basic_to_detailed"].can_transition(session):
                    return "detailed"
            elif current_phase == "detailed":
                if await self.transition_rules["detailed_to_advanced"].can_transition(session):
                    return "advanced"
            elif current_phase == "advanced":
                if await self.transition_rules["completion"].can_transition(session):
                    return "completed"
        
        return current_phase  # 保持当前阶段

class BasicCollectionPhase:
    """基础收集阶段"""
    
    def __init__(self):
        self.required_params = [
            "age", "gender", "fitness_goal", "experience_level"
        ]
        self.optional_params = [
            "height", "weight", "activity_level"
        ]
    
    async def get_next_parameter(self, session: CollectionSession) -> Optional[str]:
        """获取下一个需要收集的参数"""
        # 优先收集必需参数
        for param in self.required_params:
            if param not in session.collected_params:
                return param
        
        # 然后收集可选参数
        for param in self.optional_params:
            if param not in session.collected_params:
                # 检查是否值得收集这个参数
                if await self._is_parameter_valuable(param, session):
                    return param
        
        return None  # 基础阶段完成

class DetailedCollectionPhase:
    """详细收集阶段"""
    
    def __init__(self):
        self.detailed_params = [
            "training_frequency", "preferred_workout_time", 
            "equipment_access", "injury_history", "dietary_preferences"
        ]
    
    async def get_next_parameter(self, session: CollectionSession) -> Optional[str]:
        """获取下一个详细参数"""
        # 基于用户目标和基础信息，智能选择详细参数
        relevant_params = await self._filter_relevant_params(session)
        
        for param in relevant_params:
            if param not in session.collected_params:
                return param
        
        return None
```

### 智能问题生成器

```python
class IntelligentQuestionGenerator:
    """智能问题生成器"""
    
    def __init__(self, llm_proxy: LLMProxyService):
        self.llm_proxy = llm_proxy
        self.question_templates = QuestionTemplateManager()
        self.personalization_engine = QuestionPersonalizationEngine()
        
    async def generate_next_question(self, session: CollectionSession) -> Question:
        """生成下一个问题"""
        # 确定需要收集的参数
        next_param = await self._determine_next_parameter(session)
        
        if not next_param:
            return None  # 收集完成
        
        # 生成个性化问题
        question = await self._generate_personalized_question(
            parameter=next_param,
            session=session
        )
        
        # 添加智能提示和预填充
        await self._add_smart_suggestions(question, session)
        
        return question
    
    async def _generate_personalized_question(self, parameter: str, 
                                            session: CollectionSession) -> Question:
        """生成个性化问题"""
        # 构建上下文
        context = {
            "user_profile": session.user_profile,
            "collected_params": session.collected_params,
            "target_intent": session.target_intent,
            "conversation_history": session.context.get("conversation_history", [])
        }
        
        # 使用LLM生成个性化问题
        prompt = self._build_question_generation_prompt(parameter, context)
        
        response = await self.llm_proxy.aget_chat_response([
            {"role": "system", "content": "你是一个专业的健身顾问，擅长以自然、友好的方式收集用户信息。"},
            {"role": "user", "content": prompt}
        ], model="intent-recognition-app")
        
        # 解析响应生成问题对象
        question_data = self._parse_question_response(response)
        
        return Question(
            parameter=parameter,
            text=question_data["text"],
            type=question_data["type"],
            options=question_data.get("options", []),
            suggestions=question_data.get("suggestions", []),
            validation_rules=question_data.get("validation", {}),
            personalization_score=question_data.get("personalization_score", 0.5)
        )
    
    def _build_question_generation_prompt(self, parameter: str, context: Dict[str, Any]) -> str:
        """构建问题生成提示"""
        return f"""
        基于以下信息，生成一个自然、个性化的问题来收集用户的{parameter}信息：

        用户画像：{context.get('user_profile', {})}
        已收集信息：{context.get('collected_params', {})}
        目标意图：{context.get('target_intent', '')}

        要求：
        1. 问题要自然、友好，避免生硬的表单式询问
        2. 基于已有信息进行个性化
        3. 提供智能建议选项
        4. 包含适当的解释和引导

        返回JSON格式：
        {{
            "text": "问题文本",
            "type": "问题类型(choice/input/range)",
            "options": ["选项1", "选项2"],
            "suggestions": ["建议1", "建议2"],
            "validation": {{"min": 0, "max": 100}},
            "personalization_score": 0.8
        }}
        """
```

### 用户行为学习器

```python
class UserBehaviorLearner:
    """用户行为学习器"""
    
    def __init__(self):
        self.behavior_patterns: Dict[int, UserBehaviorPattern] = {}
        self.learning_algorithms = {
            "preference_learning": PreferenceLearningAlgorithm(),
            "response_pattern": ResponsePatternAnalyzer(),
            "completion_predictor": CompletionPredictor()
        }
    
    async def learn_from_interaction(self, user_id: int, interaction: UserInteraction):
        """从用户交互中学习"""
        if user_id not in self.behavior_patterns:
            self.behavior_patterns[user_id] = UserBehaviorPattern(user_id)
        
        pattern = self.behavior_patterns[user_id]
        
        # 记录交互
        pattern.add_interaction(interaction)
        
        # 更新学习模型
        await self._update_learning_models(user_id, interaction)
    
    async def predict_user_preference(self, user_id: int, parameter: str) -> Dict[str, Any]:
        """预测用户偏好"""
        if user_id not in self.behavior_patterns:
            return {"confidence": 0.0, "prediction": None}
        
        pattern = self.behavior_patterns[user_id]
        
        # 使用多种算法进行预测
        predictions = {}
        
        for algo_name, algorithm in self.learning_algorithms.items():
            prediction = await algorithm.predict(pattern, parameter)
            predictions[algo_name] = prediction
        
        # 综合预测结果
        final_prediction = await self._combine_predictions(predictions)
        
        return final_prediction
    
    async def get_optimal_question_order(self, user_id: int, parameters: List[str]) -> List[str]:
        """获取最优问题顺序"""
        if user_id not in self.behavior_patterns:
            return parameters  # 使用默认顺序
        
        pattern = self.behavior_patterns[user_id]
        
        # 分析用户的回答模式
        response_analysis = await self._analyze_response_patterns(pattern)
        
        # 基于分析结果重新排序问题
        optimized_order = await self._optimize_question_order(
            parameters, response_analysis
        )
        
        return optimized_order

class UserBehaviorPattern:
    """用户行为模式"""
    
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.interactions: List[UserInteraction] = []
        self.preferences: Dict[str, Any] = {}
        self.response_times: Dict[str, List[float]] = {}
        self.completion_rates: Dict[str, float] = {}
        self.question_preferences: Dict[str, float] = {}
    
    def add_interaction(self, interaction: UserInteraction):
        """添加用户交互记录"""
        self.interactions.append(interaction)
        
        # 更新响应时间统计
        param = interaction.parameter
        if param not in self.response_times:
            self.response_times[param] = []
        self.response_times[param].append(interaction.response_time)
        
        # 更新偏好分析
        self._update_preferences(interaction)
    
    def _update_preferences(self, interaction: UserInteraction):
        """更新用户偏好"""
        # 分析用户的回答模式
        if interaction.response_type == "quick_select":
            self.preferences["prefers_quick_options"] = True
        elif interaction.response_type == "detailed_input":
            self.preferences["prefers_detailed_input"] = True
        
        # 分析问题类型偏好
        question_type = interaction.question_type
        if question_type not in self.question_preferences:
            self.question_preferences[question_type] = 0.5
        
        # 基于用户反馈调整偏好分数
        if interaction.user_satisfaction > 0.7:
            self.question_preferences[question_type] += 0.1
        else:
            self.question_preferences[question_type] -= 0.1
        
        # 保持分数在合理范围内
        self.question_preferences[question_type] = max(0.0, min(1.0, 
            self.question_preferences[question_type]))
```

### 上下文记忆管理器

```python
class ContextMemoryManager:
    """上下文记忆管理器"""
    
    def __init__(self):
        self.memory_store: Dict[str, ContextMemory] = {}
        self.retention_policy = MemoryRetentionPolicy()
        self.compression_manager = MemoryCompressionManager()
    
    async def store_context(self, session_id: str, context: Dict[str, Any], 
                          retention_hours: int = 24):
        """存储上下文信息"""
        memory = ContextMemory(
            session_id=session_id,
            context=context,
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(hours=retention_hours),
            access_count=0
        )
        
        # 压缩大型上下文
        if self._should_compress_context(context):
            memory.context = await self.compression_manager.compress_context(context)
            memory.is_compressed = True
        
        self.memory_store[session_id] = memory
    
    async def retrieve_context(self, session_id: str) -> Optional[Dict[str, Any]]:
        """检索上下文信息"""
        if session_id not in self.memory_store:
            return None
        
        memory = self.memory_store[session_id]
        
        # 检查是否过期
        if datetime.now() > memory.expires_at:
            del self.memory_store[session_id]
            return None
        
        # 更新访问统计
        memory.access_count += 1
        memory.last_accessed = datetime.now()
        
        # 解压缩上下文
        context = memory.context
        if memory.is_compressed:
            context = await self.compression_manager.decompress_context(context)
        
        return context
    
    async def update_context(self, session_id: str, updates: Dict[str, Any]):
        """更新上下文信息"""
        if session_id in self.memory_store:
            memory = self.memory_store[session_id]
            
            # 解压缩（如果需要）
            context = memory.context
            if memory.is_compressed:
                context = await self.compression_manager.decompress_context(context)
            
            # 更新上下文
            context.update(updates)
            
            # 重新压缩（如果需要）
            if self._should_compress_context(context):
                context = await self.compression_manager.compress_context(context)
                memory.is_compressed = True
            
            memory.context = context
            memory.last_modified = datetime.now()
    
    async def cleanup_expired_contexts(self):
        """清理过期的上下文"""
        current_time = datetime.now()
        expired_sessions = [
            session_id for session_id, memory in self.memory_store.items()
            if current_time > memory.expires_at
        ]
        
        for session_id in expired_sessions:
            del self.memory_store[session_id]
        
        logger.info(f"清理了 {len(expired_sessions)} 个过期上下文")
```

### 个性化推荐引擎

```python
class PersonalizationEngine:
    """个性化推荐引擎"""
    
    def __init__(self):
        self.recommendation_algorithms = {
            "collaborative_filtering": CollaborativeFilteringRecommender(),
            "content_based": ContentBasedRecommender(),
            "hybrid": HybridRecommender()
        }
        self.user_similarity_calculator = UserSimilarityCalculator()
    
    async def generate_parameter_suggestions(self, user_id: int, parameter: str, 
                                           context: Dict[str, Any]) -> List[Suggestion]:
        """生成参数建议"""
        suggestions = []
        
        # 基于用户历史的建议
        historical_suggestions = await self._get_historical_suggestions(user_id, parameter)
        suggestions.extend(historical_suggestions)
        
        # 基于相似用户的建议
        similar_user_suggestions = await self._get_similar_user_suggestions(
            user_id, parameter, context
        )
        suggestions.extend(similar_user_suggestions)
        
        # 基于内容的建议
        content_suggestions = await self._get_content_based_suggestions(
            parameter, context
        )
        suggestions.extend(content_suggestions)
        
        # 排序和去重
        suggestions = await self._rank_and_deduplicate_suggestions(suggestions)
        
        return suggestions[:5]  # 返回前5个建议
    
    async def predict_parameter_value(self, user_id: int, parameter: str, 
                                    confidence_threshold: float = 0.8) -> Optional[Any]:
        """预测参数值"""
        predictions = []
        
        for algo_name, algorithm in self.recommendation_algorithms.items():
            prediction = await algorithm.predict_value(user_id, parameter)
            if prediction and prediction.confidence >= confidence_threshold:
                predictions.append(prediction)
        
        if not predictions:
            return None
        
        # 选择置信度最高的预测
        best_prediction = max(predictions, key=lambda p: p.confidence)
        return best_prediction.value
    
    async def _get_similar_user_suggestions(self, user_id: int, parameter: str, 
                                          context: Dict[str, Any]) -> List[Suggestion]:
        """获取相似用户的建议"""
        # 找到相似用户
        similar_users = await self.user_similarity_calculator.find_similar_users(
            user_id, limit=10
        )
        
        suggestions = []
        for similar_user_id, similarity_score in similar_users:
            # 获取相似用户对该参数的选择
            user_choice = await self._get_user_parameter_choice(similar_user_id, parameter)
            if user_choice:
                suggestion = Suggestion(
                    value=user_choice.value,
                    confidence=similarity_score * user_choice.confidence,
                    source="similar_users",
                    explanation=f"基于与您相似的用户选择"
                )
                suggestions.append(suggestion)
        
        return suggestions

class Suggestion:
    """建议对象"""
    
    def __init__(self, value: Any, confidence: float, source: str, explanation: str):
        self.value = value
        self.confidence = confidence
        self.source = source
        self.explanation = explanation
        self.created_at = datetime.now()
```

## 配置和部署

### 配置文件示例

```yaml
# progressive_parameter_collector_config.yml
parameter_collector:
  # 收集策略配置
  collection_strategy:
    max_questions_per_session: 5
    phases:
      basic:
        required_params: ["age", "gender", "fitness_goal", "experience_level"]
        optional_params: ["height", "weight", "activity_level"]
        completion_threshold: 0.8
      detailed:
        params: ["training_frequency", "preferred_workout_time", "equipment_access"]
        relevance_threshold: 0.7
      advanced:
        params: ["injury_history", "dietary_preferences", "supplement_usage"]
        personalization_threshold: 0.8
  
  # 问题生成配置
  question_generation:
    personalization_enabled: true
    llm_model: "intent-recognition-app"
    response_timeout: 10  # seconds
    max_retries: 3
    template_fallback: true
  
  # 学习算法配置
  behavior_learning:
    enabled: true
    learning_rate: 0.1
    min_interactions: 5
    confidence_threshold: 0.8
    pattern_analysis_window: 30  # days
  
  # 上下文管理配置
  context_management:
    retention_hours: 24
    compression_threshold: 1024  # bytes
    cleanup_interval: 3600  # seconds
    max_memory_usage: "100MB"
  
  # 个性化推荐配置
  personalization:
    enabled: true
    similarity_threshold: 0.7
    max_suggestions: 5
    confidence_threshold: 0.8
    algorithms:
      - "collaborative_filtering"
      - "content_based"
      - "hybrid"
```

## 性能监控

### 关键指标

```python
class ParameterCollectionMetrics:
    """参数收集性能指标"""
    
    def __init__(self):
        self.metrics = {
            "collection_efficiency": MetricGauge(),
            "user_satisfaction": MetricGauge(),
            "completion_rate": MetricGauge(),
            "question_generation_time": MetricHistogram(),
            "prediction_accuracy": MetricGauge(),
            "personalization_effectiveness": MetricGauge()
        }
    
    def record_collection_session(self, session: CollectionSession):
        """记录收集会话指标"""
        # 计算收集效率
        efficiency = len(session.collected_params) / len(session.questions_asked)
        self.metrics["collection_efficiency"].set(efficiency)
        
        # 记录完成率
        completion_rate = session.completion_percentage
        self.metrics["completion_rate"].set(completion_rate)
        
        # 记录用户满意度
        if session.user_feedback:
            satisfaction = session.user_feedback.satisfaction_score
            self.metrics["user_satisfaction"].set(satisfaction)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return {
            "collection_efficiency": self.metrics["collection_efficiency"].value(),
            "average_completion_rate": self.metrics["completion_rate"].value(),
            "user_satisfaction_score": self.metrics["user_satisfaction"].value(),
            "avg_question_generation_time": self.metrics["question_generation_time"].average(),
            "prediction_accuracy": self.metrics["prediction_accuracy"].value()
        }
```

## 测试策略

### 用户体验测试

```python
async def test_user_experience():
    """用户体验测试"""
    collector = ProgressiveParameterCollector(db, llm_proxy)
    
    # 模拟不同类型的用户
    test_users = [
        {"type": "new_user", "profile": {}},
        {"type": "returning_user", "profile": {"age": 25, "gender": "male"}},
        {"type": "experienced_user", "profile": {"fitness_goal": "muscle_gain", "experience_level": "advanced"}}
    ]
    
    results = []
    
    for user_data in test_users:
        # 开始收集会话
        session = await collector.start_collection(
            user_id=user_data["user_id"],
            target_intent="training_plan_generation",
            context={"user_profile": user_data["profile"]}
        )
        
        # 模拟用户交互
        interaction_results = await simulate_user_interactions(session, collector)
        
        results.append({
            "user_type": user_data["type"],
            "questions_asked": len(session.questions_asked),
            "completion_time": interaction_results["completion_time"],
            "user_satisfaction": interaction_results["satisfaction_score"],
            "collection_efficiency": interaction_results["efficiency"]
        })
    
    return results
```

---

*此规范文档将指导渐进式参数收集器的完整实现，确保用户信息收集的智能化和高效化。* 