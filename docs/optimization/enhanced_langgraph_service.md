# EnhancedLangGraphService 实现规范

## 概述

EnhancedLangGraphService 是系统的核心工作流编排服务，提供统一的接口来处理不同类型的用户请求。它支持5种工作模式，每种模式针对特定的使用场景进行优化。

## 架构设计

### 核心组件

```python
class EnhancedLangGraphService:
    """统一工作流编排服务"""
    
    def __init__(self, db: Session, config: Dict[str, Any] = None):
        self.db = db
        self.config = config or {}
        
        # 工作流模式
        self.workflow_modes = {
            "standard": StandardWorkflow(self),
            "enhanced": EnhancedWorkflow(self),
            "streaming": StreamingWorkflow(self),
            "parameter_collection": ParameterCollectionWorkflow(self),
            "training_plan": TrainingPlanWorkflow(self)
        }
        
        # 核心服务
        self.llm_proxy = LLMProxyService()
        self.intent_recognizer = EnhancedIntentRecognizer(self.llm_proxy)
        self.parameter_extractor = EnhancedParameterExtractor(self.llm_proxy)
        self.context_manager = ConversationContextManager()
        
        # 监控和优化
        self.performance_monitor = WorkflowPerformanceMonitor()
        self.error_handler = WorkflowErrorHandler()
        self.cache_manager = CacheManager()
        
        # 处理管道
        self.pipeline = ProcessingPipeline([
            PreprocessingStage(),
            IntentRecognitionStage(),
            ParameterExtractionStage(),
            ExecutionStage(),
            PostprocessingStage()
        ])
```

### 工作流模式详解

#### 1. Standard Workflow (标准工作流)
- **用途**: 处理一般性对话和简单查询
- **特点**: 快速响应，低延迟
- **处理时间**: <100ms
- **适用场景**: 问候、简单问答、状态查询

#### 2. Enhanced Workflow (增强工作流)
- **用途**: 复杂查询和多步骤任务
- **特点**: 深度分析，上下文感知
- **处理时间**: 100-500ms
- **适用场景**: 健身建议、营养分析、复杂推理

#### 3. Streaming Workflow (流式工作流)
- **用途**: 长文本生成和实时响应
- **特点**: 流式输出，用户体验优化
- **处理时间**: 持续流式
- **适用场景**: 训练计划生成、详细解释

#### 4. Parameter Collection Workflow (参数收集工作流)
- **用途**: 智能收集用户信息
- **特点**: 渐进式收集，智能提示
- **处理时间**: <200ms
- **适用场景**: 用户画像建立、偏好设置

#### 5. Training Plan Workflow (训练计划工作流)
- **用途**: 个性化训练计划生成
- **特点**: 结构化输出，数据驱动
- **处理时间**: 200-1000ms
- **适用场景**: 训练计划制定、计划调整

## 实现细节

### 多阶段处理管道

```python
class ProcessingPipeline:
    """多阶段处理管道"""
    
    def __init__(self, stages: List[ProcessingStage]):
        self.stages = stages
        self.metrics = PipelineMetrics()
    
    async def process(self, request: WorkflowRequest) -> WorkflowResponse:
        """执行完整的处理管道"""
        context = ProcessingContext(request)
        
        for stage in self.stages:
            start_time = time.time()
            try:
                context = await stage.process(context)
                self.metrics.record_stage_time(stage.name, time.time() - start_time)
            except Exception as e:
                await self._handle_stage_error(stage, context, e)
                break
        
        return context.response

class PreprocessingStage(ProcessingStage):
    """预处理阶段"""
    
    async def process(self, context: ProcessingContext) -> ProcessingContext:
        # 输入验证
        await self._validate_input(context.request)
        
        # 文本清理和标准化
        context.cleaned_input = await self._clean_text(context.request.message)
        
        # 上下文加载
        context.conversation_history = await self._load_context(
            context.request.session_id
        )
        
        return context

class IntentRecognitionStage(ProcessingStage):
    """意图识别阶段"""
    
    async def process(self, context: ProcessingContext) -> ProcessingContext:
        # 快速意图检测
        if context.request.quick_intent:
            context.intent = context.request.quick_intent
            context.confidence = 1.0
        else:
            # 智能意图识别
            intent_result = await self.intent_recognizer.recognize_intent(
                message=context.cleaned_input,
                context=context.conversation_history,
                user_profile=context.user_profile
            )
            context.intent = intent_result.intent
            context.confidence = intent_result.confidence
        
        # 意图验证和修正
        if context.confidence < 0.7:
            context.intent = await self._clarify_intent(context)
        
        return context
```

### 智能工作流选择

```python
class WorkflowSelector:
    """智能工作流选择器"""
    
    def __init__(self):
        self.selection_rules = [
            StreamingRule(),
            ParameterCollectionRule(),
            TrainingPlanRule(),
            EnhancedRule(),
            StandardRule()  # 默认规则，最后执行
        ]
    
    async def select_workflow(self, context: ProcessingContext) -> str:
        """选择最适合的工作流"""
        for rule in self.selection_rules:
            if await rule.matches(context):
                return rule.workflow_name
        
        return "standard"  # 默认工作流

class StreamingRule(SelectionRule):
    """流式工作流选择规则"""
    
    async def matches(self, context: ProcessingContext) -> bool:
        # 检查是否需要流式响应
        return (
            context.request.stream_response or
            context.intent in ["training_plan_generation", "detailed_explanation"] or
            len(context.expected_response) > 500
        )

class ParameterCollectionRule(SelectionRule):
    """参数收集工作流选择规则"""
    
    async def matches(self, context: ProcessingContext) -> bool:
        # 检查是否需要收集参数
        missing_params = await self._check_missing_parameters(context)
        return len(missing_params) > 0
```

### 性能监控和优化

```python
class WorkflowPerformanceMonitor:
    """工作流性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.thresholds = {
            "standard": 100,      # ms
            "enhanced": 500,      # ms
            "streaming": 1000,    # ms
            "parameter_collection": 200,  # ms
            "training_plan": 1000  # ms
        }
    
    async def monitor_workflow(self, workflow_name: str, execution_time: float):
        """监控工作流执行"""
        self.metrics[workflow_name].append(execution_time)
        
        # 检查性能阈值
        threshold = self.thresholds.get(workflow_name, 500)
        if execution_time > threshold:
            await self._handle_performance_issue(workflow_name, execution_time)
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        report = {}
        for workflow_name, times in self.metrics.items():
            if times:
                report[workflow_name] = {
                    "avg_time": sum(times) / len(times),
                    "max_time": max(times),
                    "min_time": min(times),
                    "total_executions": len(times),
                    "threshold": self.thresholds.get(workflow_name, 500)
                }
        return report
```

### 错误处理和恢复

```python
class WorkflowErrorHandler:
    """工作流错误处理器"""
    
    def __init__(self):
        self.error_strategies = {
            "timeout": TimeoutRecoveryStrategy(),
            "llm_error": LLMErrorRecoveryStrategy(),
            "parameter_error": ParameterErrorRecoveryStrategy(),
            "system_error": SystemErrorRecoveryStrategy()
        }
    
    async def handle_error(self, error: Exception, context: ProcessingContext) -> WorkflowResponse:
        """处理工作流错误"""
        error_type = self._classify_error(error)
        strategy = self.error_strategies.get(error_type, self.error_strategies["system_error"])
        
        return await strategy.recover(error, context)

class LLMErrorRecoveryStrategy(RecoveryStrategy):
    """LLM错误恢复策略"""
    
    async def recover(self, error: Exception, context: ProcessingContext) -> WorkflowResponse:
        # 尝试使用备用模型
        try:
            backup_response = await self._try_backup_model(context)
            if backup_response:
                return backup_response
        except Exception:
            pass
        
        # 返回预定义响应
        return WorkflowResponse(
            content="抱歉，AI服务暂时不可用，请稍后重试。",
            error=True,
            error_type="llm_unavailable"
        )
```

## 配置和部署

### 配置文件示例

```yaml
# enhanced_langgraph_config.yml
enhanced_langgraph:
  # 工作流配置
  workflows:
    standard:
      timeout: 100  # ms
      cache_enabled: true
      cache_ttl: 300  # seconds
    
    enhanced:
      timeout: 500
      cache_enabled: true
      cache_ttl: 600
      deep_analysis: true
    
    streaming:
      chunk_size: 50  # characters
      stream_delay: 10  # ms
      buffer_size: 1000
    
    parameter_collection:
      max_questions: 5
      collection_strategy: "progressive"
      learning_enabled: true
    
    training_plan:
      timeout: 1000
      structured_output: true
      validation_enabled: true
  
  # 性能配置
  performance:
    monitoring_enabled: true
    auto_optimization: true
    metrics_retention: 7  # days
    alert_thresholds:
      response_time: 1000  # ms
      error_rate: 0.05     # 5%
  
  # 缓存配置
  cache:
    enabled: true
    backend: "redis"
    default_ttl: 3600
    max_size: "100MB"
```

### 部署检查清单

- [ ] 数据库连接配置正确
- [ ] Redis缓存服务可用
- [ ] LLM服务API密钥配置
- [ ] 性能监控组件启用
- [ ] 错误处理策略配置
- [ ] 日志记录级别设置
- [ ] 健康检查端点可访问

## 测试策略

### 单元测试
- 各工作流模式独立测试
- 处理管道各阶段测试
- 错误处理策略测试

### 集成测试
- 端到端工作流测试
- 性能基准测试
- 并发负载测试

### 性能测试
- 响应时间测试
- 吞吐量测试
- 内存使用测试
- 缓存效率测试

## 监控指标

### 关键性能指标 (KPI)
- 平均响应时间: <200ms
- 95%响应时间: <500ms
- 错误率: <1%
- 缓存命中率: >80%

### 业务指标
- 工作流选择准确率: >95%
- 用户满意度: >90%
- 任务完成率: >98%

---

*此规范文档将指导EnhancedLangGraphService的完整实现，确保系统的高性能、高可用性和优秀的用户体验。* 