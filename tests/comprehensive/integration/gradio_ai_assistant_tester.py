#!/usr/bin/env python3
"""
智能健身AI助手系统 - Gradio实时测试平台

这是一个用于测试AI助手系统的Gradio界面，直接连接conversation_orchestrator进行实时测试。

特性：
- 实时对话测试
- 性能监控
- 状态跟踪
- 预设测试场景
- 数据导出

运行方式:
source .venv/bin/activate && python tests/comprehensive/integration/gradio_ai_assistant_tester.py

访问地址:
http://localhost:7860
"""

import sys
import os
import asyncio
import logging
import time
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import gradio as gr
    logger.info("✅ Gradio导入成功")
except ImportError as e:
    logger.error(f"❌ Gradio导入失败: {e}")
    print("请安装Gradio: pip install gradio")
    sys.exit(1)

# 尝试导入项目依赖 - 增强错误处理
try:
    from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
    logger.info("✅ ConversationOrchestrator导入成功")
    ORCHESTRATOR_AVAILABLE = True
except ImportError as e:
    logger.error(f"❌ ConversationOrchestrator导入失败: {e}")
    ORCHESTRATOR_AVAILABLE = False

# 检测测试模式
TEST_MODE = not ORCHESTRATOR_AVAILABLE or os.getenv("TEST_MODE", "false").lower() == "true"

if TEST_MODE:
    logger.warning("🔧 运行在测试模式 - 将使用Mock回复")

class AIAssistantTester:
    """AI助手测试器"""
    
    def __init__(self):
        self.user_id = 1
        self.wechat_openid = "oCU0j7Rg9kzigLzquCBje3KfnQXk"
        self.current_session_id = str(uuid.uuid4())
        self.conversation_history = []
        self.conversation_state = {}
        self.performance_metrics = []
        
        # 性能统计
        self.total_requests = 0
        self.total_response_time = 0
        self.success_count = 0
        self.error_count = 0
        
        logger.info(f"🤖 AI助手测试器初始化完成")
        logger.info(f"📊 测试配置 - 用户ID: {self.user_id}, 会话ID: {self.current_session_id}")

    async def send_message_to_ai(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送消息给AI系统"""
        start_time = time.time()
        
        try:
            if TEST_MODE:
                # 测试模式：返回Mock回复
                await asyncio.sleep(0.1)  # 模拟处理延迟
                response_time = (time.time() - start_time) * 1000
                
                mock_response = {
                    "response": f"🔧 测试模式回复：收到您的消息「{message[:30]}{'...' if len(message) > 30 else ''}」",
                    "intent_type": "general_chat",
                    "confidence": 0.85,
                    "conversation_state": {
                        "user_profile": {
                            "name": "测试用户",
                            "fitness_goal": "增肌"
                        },
                        "current_intent": "general_chat",
                        "conversation_phase": "active"
                    },
                    "meta_info": {
                        "processing_time_ms": response_time,
                        "source": "test_mode"
                    }
                }
                
                logger.info(f"🔧 测试模式回复生成完成，响应时间: {response_time:.2f}ms")
                return {
                    "success": True,
                    "response": mock_response["response"],
                    "intent": mock_response["intent_type"],
                    "confidence": mock_response["confidence"],
                    "response_time": response_time,
                    "meta_info": mock_response["meta_info"],
                    "conversation_state": mock_response["conversation_state"],
                    "missing_parameters": [],
                    "next_intent": None
                }
            
            # 正常模式：使用真实的AI系统
            if not ORCHESTRATOR_AVAILABLE:
                raise Exception("ConversationOrchestrator不可用")
            
            # 准备用户信息字典
            user_info = {
                "user_id": str(self.user_id),
                "wechat_openid": self.wechat_openid,
                "nickname": "测试用户",
                "gender": "male",
                "height": 175,
                "weight": 70,
                "fitness_goal": "增肌",
                "experience_level": "初级",
                "activity_level": "中等"
            }
            
            # 调用对话编排器 - 修正参数列表以匹配实际方法签名
            response = await conversation_orchestrator.process_message(
                message=message,
                conversation_id=self.current_session_id,
                user_info=user_info
            )
            
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000  # 毫秒
            
            # 更新统计
            self.total_requests += 1
            self.total_response_time += response_time
            self.success_count += 1
            
            # 记录性能指标
            self.performance_metrics.append({
                "timestamp": datetime.now().strftime("%H:%M:%S"),
                "response_time": round(response_time, 2),
                "message_length": len(message),
                "response_length": len(response.get("response_content", "")),
                "intent": response.get("intent", "unknown"),
                "confidence": response.get("confidence", 0),
                "status": "success"
            })
            
            # 更新对话状态
            if "conversation_state" in response:
                self.conversation_state = response["conversation_state"]
            
            return {
                "success": True,
                "response": response.get("response_content", response.get("response", "")),
                "intent": response.get("intent"),
                "confidence": response.get("confidence"),
                "response_time": response_time,
                "meta_info": response.get("meta_info", {}),
                "conversation_state": response.get("conversation_state", {}),
                "missing_parameters": response.get("missing_parameters", []),
                "next_intent": response.get("next_intent")
            }
            
        except Exception as e:
            self.error_count += 1
            error_msg = f"错误: {str(e)}"
            logger.error(f"发送消息失败: {error_msg}", exc_info=True)
            
            # 记录错误指标
            self.performance_metrics.append({
                "timestamp": datetime.now().strftime("%H:%M:%S"),
                "response_time": 0,
                "message_length": len(message),
                "response_length": 0,
                "intent": "error",
                "confidence": 0,
                "status": "error"
            })
            
            return {
                "success": False,
                "response": error_msg,
                "intent": "error",
                "confidence": 0,
                "response_time": 0,
                "error": str(e)
            }

    def format_conversation_history(self) -> str:
        """格式化对话历史"""
        if not self.conversation_history:
            return "暂无对话历史"
        
        formatted = []
        for i, msg in enumerate(self.conversation_history, 1):
            timestamp = msg.get("timestamp", "")
            role = "🧑‍💻 用户" if msg["role"] == "user" else "🤖 AI助手"
            content = msg["content"]
            
            formatted.append(f"{i}. [{timestamp}] {role}:\n{content}\n")
            
            # 如果是AI回复，添加元信息
            if msg["role"] == "assistant" and msg.get("meta_info"):
                meta = msg["meta_info"]
                if meta.get("intent"):
                    formatted.append(f"   📋 意图: {meta['intent']} (置信度: {meta.get('confidence', 0):.2f})")
                if meta.get("response_time"):
                    formatted.append(f"   ⏱️ 响应时间: {meta['response_time']:.2f}ms")
                formatted.append("")
        
        return "\n".join(formatted)

    def format_performance_metrics(self) -> str:
        """格式化性能指标"""
        if not self.performance_metrics:
            return "暂无性能数据"
        
        # 计算统计数据
        avg_response_time = self.total_response_time / max(self.total_requests, 1)
        success_rate = (self.success_count / max(self.total_requests, 1)) * 100
        
        # 最近10条记录
        recent_metrics = self.performance_metrics[-10:]
        
        stats = f"""📊 总体统计:
• 总请求数: {self.total_requests}
• 成功率: {success_rate:.1f}%
• 平均响应时间: {avg_response_time:.2f}ms
• 错误数: {self.error_count}

📈 最近10条记录:
"""
        
        for metric in recent_metrics:
            status_icon = "✅" if metric["status"] == "success" else "❌"
            stats += f"{status_icon} [{metric['timestamp']}] {metric['response_time']}ms | {metric['intent']} | 置信度: {metric['confidence']:.2f}\n"
        
        return stats

    def format_conversation_state(self) -> str:
        """格式化对话状态"""
        if not self.conversation_state:
            return "暂无状态信息"
        
        state_info = []
        
        # 用户信息
        if "user_profile" in self.conversation_state:
            profile = self.conversation_state["user_profile"]
            state_info.append("👤 用户档案:")
            for key, value in profile.items():
                if value:
                    state_info.append(f"  • {key}: {value}")
        
        # 训练参数
        if "training_parameters" in self.conversation_state:
            params = self.conversation_state["training_parameters"]
            state_info.append("\n🏋️ 训练参数:")
            for key, value in params.items():
                if value:
                    state_info.append(f"  • {key}: {value}")
        
        # 当前意图
        if "current_intent" in self.conversation_state:
            intent = self.conversation_state["current_intent"]
            state_info.append(f"\n🎯 当前意图: {intent}")
        
        # 对话阶段
        if "conversation_phase" in self.conversation_state:
            phase = self.conversation_state["conversation_phase"]
            state_info.append(f"📍 对话阶段: {phase}")
        
        return "\n".join(state_info) if state_info else "暂无详细状态信息"

    async def process_message(self, message: str, context: str = "") -> Tuple[str, str, str, str]:
        """处理消息并返回更新的界面内容"""
        if not message.strip():
            return (
                self.format_conversation_history(),
                self.format_performance_metrics(),
                self.format_conversation_state(),
                ""
            )
        
        # 解析上下文
        context_dict = {}
        if context.strip():
            try:
                context_dict = json.loads(context)
            except json.JSONDecodeError:
                context_dict = {"note": context}
        
        # 添加用户消息到历史
        self.conversation_history.append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        })
        
        # 发送消息给AI
        ai_response = await self.send_message_to_ai(message, context_dict)
        
        # 添加AI回复到历史
        self.conversation_history.append({
            "role": "assistant",
            "content": ai_response["response"],
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "meta_info": {
                "intent": ai_response.get("intent"),
                "confidence": ai_response.get("confidence"),
                "response_time": ai_response.get("response_time"),
                "missing_parameters": ai_response.get("missing_parameters"),
                "next_intent": ai_response.get("next_intent")
            }
        })
        
        return (
            self.format_conversation_history(),
            self.format_performance_metrics(),
            self.format_conversation_state(),
            ""  # 清空输入框
        )

    def export_conversation_data(self) -> str:
        """导出对话数据"""
        export_data = {
            "session_id": self.current_session_id,
            "user_id": self.user_id,
            "wechat_openid": self.wechat_openid,
            "test_duration": time.time() - self.test_start_time,
            "conversation_history": self.conversation_history,
            "performance_metrics": self.performance_metrics,
            "conversation_state": self.conversation_state,
            "statistics": {
                "total_requests": self.total_requests,
                "success_count": self.success_count,
                "error_count": self.error_count,
                "avg_response_time": self.total_response_time / max(self.total_requests, 1),
                "success_rate": (self.success_count / max(self.total_requests, 1)) * 100
            }
        }
        
        return json.dumps(export_data, ensure_ascii=False, indent=2)

    def reset_conversation(self) -> Tuple[str, str, str]:
        """重置对话"""
        self.current_session_id = str(uuid.uuid4())
        self.conversation_history = []
        self.conversation_state = {}
        self.test_start_time = time.time()
        
        return (
            "对话已重置，开始新的会话",
            self.format_performance_metrics(),
            self.format_conversation_state()
        )

# 全局测试器实例
tester = AIAssistantTester()

def create_gradio_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(
        title="智能健身AI助手系统测试平台",
        css="""
        .gradio-container {
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .conversation-box {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .metrics-box {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f0f8ff;
        }
        .state-box {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f5fffa;
        }
        """
    ) as interface:
        
        gr.Markdown("""
        # 🤖 智能健身AI助手系统测试平台
        
        **实时连接生产系统 v2/endpoints/chat.py**
        
        - 🔗 **直接API集成**: 连接实际的对话编排器
        - 📊 **性能监控**: 实时响应时间和成功率统计
        - 🎯 **状态跟踪**: 对话状态、意图识别和参数收集
        - 📁 **数据导出**: 完整的测试数据和分析报告
        """)
        
        with gr.Row():
            gr.Markdown(f"""
            **测试配置:**
            - 用户ID: `{tester.user_id}`
            - 微信OpenID: `{tester.wechat_openid}`
            - 会话ID: `{tester.current_session_id}`
            """)
        
        with gr.Row():
            # 左侧：对话区域
            with gr.Column(scale=2):
                gr.Markdown("## 💬 对话历史")
                conversation_display = gr.Textbox(
                    value=tester.format_conversation_history(),
                    lines=20,
                    label="对话记录",
                    elem_classes=["conversation-box"],
                    interactive=False
                )
                
                with gr.Row():
                    message_input = gr.Textbox(
                        placeholder="请输入您的健身问题...",
                        label="发送消息",
                        lines=2
                    )
                    send_button = gr.Button("发送", variant="primary")
                
                context_input = gr.Textbox(
                    placeholder='可选：JSON格式的上下文信息，如 {"quick_intent": "training_plan"}',
                    label="上下文参数 (可选)",
                    lines=2
                )
            
            # 右侧：监控区域
            with gr.Column(scale=1):
                gr.Markdown("## 📊 性能监控")
                metrics_display = gr.Textbox(
                    value=tester.format_performance_metrics(),
                    lines=15,
                    label="性能指标",
                    elem_classes=["metrics-box"],
                    interactive=False
                )
                
                gr.Markdown("## 🔍 对话状态")
                state_display = gr.Textbox(
                    value=tester.format_conversation_state(),
                    lines=15,
                    label="状态信息",
                    elem_classes=["state-box"],
                    interactive=False
                )
        
        # 控制按钮
        with gr.Row():
            reset_button = gr.Button("🔄 重置对话", variant="secondary")
            export_button = gr.Button("📁 导出数据", variant="secondary")
        
        # 导出区域
        with gr.Row():
            export_output = gr.Textbox(
                label="导出的测试数据",
                lines=10,
                visible=False
            )
        
        # 测试场景快捷按钮
        with gr.Row():
            gr.Markdown("## 🎯 快速测试场景")
        
        with gr.Row():
            scenario_buttons = [
                gr.Button("💪 制定训练计划"),
                gr.Button("🍎 营养建议"),
                gr.Button("❓ 健身问答"),
                gr.Button("📈 进度跟踪")
            ]
        
        # 事件处理
        async def handle_send_message(message, context):
            """处理发送消息事件"""
            try:
                return await tester.process_message(message, context)
            except Exception as e:
                logger.error(f"处理消息时出错: {e}", exc_info=True)
                return (
                    f"错误: {str(e)}",
                    tester.format_performance_metrics(),
                    tester.format_conversation_state(),
                    message
                )
        
        def handle_reset():
            """处理重置事件"""
            return tester.reset_conversation()
        
        def handle_export():
            """处理导出事件"""
            return gr.update(value=tester.export_conversation_data(), visible=True)
        
        # 快速测试场景
        def create_scenario_handler(scenario_text):
            """创建场景处理器"""
            async def handler():
                return await tester.process_message(scenario_text, "")
            return handler
        
        # 绑定事件
        send_button.click(
            fn=handle_send_message,
            inputs=[message_input, context_input],
            outputs=[conversation_display, metrics_display, state_display, message_input]
        )
        
        message_input.submit(
            fn=handle_send_message,
            inputs=[message_input, context_input],
            outputs=[conversation_display, metrics_display, state_display, message_input]
        )
        
        reset_button.click(
            fn=handle_reset,
            outputs=[conversation_display, metrics_display, state_display]
        )
        
        export_button.click(
            fn=handle_export,
            outputs=[export_output]
        )
        
        # 快速测试场景
        scenarios = [
            "我想制定一个增肌训练计划",
            "请给我一些减脂期的营养建议",
            "深蹲的正确姿势是什么？",
            "如何跟踪我的健身进度？"
        ]
        
        for button, scenario in zip(scenario_buttons, scenarios):
            button.click(
                fn=create_scenario_handler(scenario),
                outputs=[conversation_display, metrics_display, state_display]
            )
    
    return interface

def main():
    """启动测试界面"""
    try:
        logger.info("启动智能健身AI助手系统测试平台...")
        
        interface = create_gradio_interface()
        
        # 获取端口配置，优先使用环境变量，否则尝试多个端口
        port = int(os.environ.get('GRADIO_SERVER_PORT', 7860))
        max_attempts = 10
        
        for attempt in range(max_attempts):
            try:
                current_port = port + attempt
                logger.info(f"尝试在端口 {current_port} 启动...")
                
                # 启动界面
                interface.launch(
                    server_name="0.0.0.0",
                    server_port=current_port,
                    share=False,
                    quiet=True  # 减少日志输出
                )
                logger.info(f"✅ 成功启动在端口 {current_port}")
                break
                
            except OSError as e:
                if "Cannot find empty port" in str(e) or "Address already in use" in str(e):
                    logger.warning(f"端口 {current_port} 被占用，尝试下一个端口...")
                    if attempt == max_attempts - 1:
                        logger.error(f"尝试了 {max_attempts} 个端口都失败，请手动指定端口")
                        raise
                    continue
                else:
                    raise
    
    except Exception as e:
        logger.error(f"启动测试平台失败: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    main()