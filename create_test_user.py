#!/usr/bin/env python3
"""
创建测试用户脚本
"""
import requests
import json

def create_test_user():
    """创建测试用户"""
    base_url = "http://localhost:8000"
    
    # 测试用户数据 - 修正格式
    user_data = {
        "openid": "test_openid_123456",  # 必需字段
        "email": "<EMAIL>",
        "nickname": "测试用户",
        "age": 25,
        "gender": 1,  # 1-男性，2-女性
        "height": 175,
        "weight": 70,
        "activity_level": 3  # 1-5的整数
    }
    
    try:
        # 尝试注册用户 - 修正路径
        response = requests.post(
            f"{base_url}/api/v1/user/register",
            json=user_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"注册响应状态码: {response.status_code}")
        print(f"注册响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 测试用户创建成功")
            return True
        elif response.status_code == 400 and "已被注册" in response.text:
            print("ℹ️ 测试用户已存在，跳过注册")
            return True
        else:
            print(f"❌ 创建用户失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 创建用户出错: {str(e)}")
        return False

def test_login():
    """测试登录"""
    base_url = "http://localhost:8000"
    
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/auth/login",
            json=login_data,  # 使用JSON格式
            headers={"Content-Type": "application/json"}
        )
        
        print(f"登录响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            print(f"✅ 登录成功，获取到token: {access_token[:20]}...")
            return access_token
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录出错: {str(e)}")
        return None

def test_chat_api(access_token):
    """测试聊天API"""
    base_url = "http://localhost:8000"
    
    chat_data = {
        "message": "你好，我想了解健身相关的信息",
        "session_id": "test-session-001"
    }
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/v2/chat/message",
            json=chat_data,
            headers=headers,
            timeout=30
        )
        
        print(f"聊天API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ 聊天API测试成功")
            print(f"AI回复: {response_data.get('response', '')[:100]}...")
            return True
        else:
            print(f"❌ 聊天API测试失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天API测试出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试用户创建和API连接...")
    
    # 1. 创建测试用户
    print("\n1. 创建测试用户...")
    if create_test_user():
        print("测试用户准备完成")
    else:
        print("测试用户创建失败，退出测试")
        exit(1)
    
    # 2. 测试登录
    print("\n2. 测试登录...")
    access_token = test_login()
    if not access_token:
        print("登录测试失败，退出测试")
        exit(1)
    
    # 3. 测试聊天API
    print("\n3. 测试聊天API...")
    if test_chat_api(access_token):
        print("聊天API测试成功")
    else:
        print("聊天API测试失败")
    
    print("\n🎉 所有测试完成！")
    print("现在可以使用以下凭据登录Gradio测试系统：")
    print("用户名: <EMAIL>")
    print("密码: testpass123")
    print("Gradio地址: http://localhost:7860") 