# 训练模板应用功能 API 使用指南

## 概述

本指南介绍如何使用新的训练模板应用功能，该功能允许将训练模板转换为具体的训练日计划，包括完整的训练动作和组记录。

## 功能特性

1. **模板应用**: 将训练模板应用到指定日期，创建完整的训练日记录
2. **数据完整性**: 自动创建 Workout、WorkoutExercise 和 SetRecord 记录
3. **模板追踪**: 在训练日中记录模板来源信息
4. **灵活配置**: 支持可选的训练计划关联

## API 端点

### 1. 应用训练模板

**端点**: `POST /api/v1/training-templates/{template_id}/apply`

**描述**: 将指定的训练模板应用到特定日期，创建完整的训练日计划

#### 请求参数

**路径参数**:
- `template_id` (int): 训练模板ID

**查询参数**:
- `date` (string, 可选): 训练日期，格式为 YYYY-MM-DD
- `training_plan_id` (int, 可选): 训练计划ID

**请求体** (application/json, 可选):
```json
{
  "date": "2024-12-20",
  "training_plan_id": 123
}
```

#### 响应示例

**成功响应** (200):
```json
{
  "message": "训练模板应用成功",
  "workout_id": 456,
  "workout": {
    "id": 456,
    "name": "上肢训练 - 2024-12-20",
    "description": "专注于上肢肌肉群的综合训练",
    "scheduled_date": "2024-12-20T00:00:00",
    "estimated_duration": 60,
    "target_body_parts": [1, 2, 3],
    "training_scenario": "gym",
    "status": "not_started",
    "template_source": {
      "template_id": 123,
      "template_name": "上肢训练",
      "applied_at": "2024-12-19T10:30:00",
      "template_description": "专注于上肢肌肉群的综合训练",
      "template_estimated_duration": 60,
      "template_target_body_parts": [1, 2, 3],
      "template_training_scenario": "gym"
    },
    "exercise_count": 5,
    "created_at": "2024-12-19T10:30:00"
  },
  "exercises": [
    {
      "id": 789,
      "exercise_id": 101,
      "exercise_name": "哑铃卧推",
      "sets": 3,
      "reps": "12",
      "weight": "20",
      "rest_seconds": 90,
      "order": 1,
      "notes": "注意动作标准",
      "exercise_type": "weight_reps",
      "superset_group": null
    }
  ],
  "applied_date": "2024-12-20"
}
```

**错误响应**:
- `400`: 参数错误（如日期格式无效）
- `404`: 模板不存在或无权访问
- `500`: 服务器内部错误

### 2. 获取从模板创建的训练日

**端点**: `GET /api/v1/training-templates/{template_id}/workouts`

**描述**: 获取从指定模板创建的所有训练日记录

#### 请求参数

**路径参数**:
- `template_id` (int): 训练模板ID

#### 响应示例

**成功响应** (200):
```json
[
  {
    "id": 456,
    "name": "上肢训练 - 2024-12-20",
    "description": "专注于上肢肌肉群的综合训练",
    "scheduled_date": "2024-12-20T00:00:00",
    "status": "completed",
    "estimated_duration": 60,
    "actual_duration": 65,
    "target_body_parts": [1, 2, 3],
    "training_scenario": "gym",
    "template_source": {
      "template_id": 123,
      "template_name": "上肢训练",
      "applied_at": "2024-12-19T10:30:00"
    },
    "exercise_count": 5,
    "created_at": "2024-12-19T10:30:00",
    "updated_at": "2024-12-20T11:35:00"
  }
]
```

## 使用示例

### 1. Python 请求示例

```python
import requests
import json
from datetime import datetime

# API 基础URL
BASE_URL = "http://localhost:8000/api/v1"

# 认证头 (假设使用 Bearer Token)
headers = {
    "Authorization": "Bearer your_access_token",
    "Content-Type": "application/json"
}

# 1. 应用训练模板
template_id = 123
apply_data = {
    "date": "2024-12-20",
    "training_plan_id": 456  # 可选
}

response = requests.post(
    f"{BASE_URL}/training-templates/{template_id}/apply",
    headers=headers,
    json=apply_data
)

if response.status_code == 200:
    result = response.json()
    print(f"成功创建训练日 ID: {result['workout_id']}")
    print(f"训练日名称: {result['workout']['name']}")
else:
    print(f"请求失败: {response.status_code} - {response.text}")

# 2. 获取从模板创建的训练日
response = requests.get(
    f"{BASE_URL}/training-templates/{template_id}/workouts",
    headers=headers
)

if response.status_code == 200:
    workouts = response.json()
    print(f"从模板创建了 {len(workouts)} 个训练日")
    for workout in workouts:
        print(f"- {workout['name']} ({workout['status']})")
```

### 2. cURL 请求示例

```bash
# 应用训练模板
curl -X POST "http://localhost:8000/api/v1/training-templates/123/apply" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2024-12-20",
    "training_plan_id": 456
  }'

# 获取从模板创建的训练日
curl -X GET "http://localhost:8000/api/v1/training-templates/123/workouts" \
  -H "Authorization: Bearer your_access_token"
```

## 数据库变更

### 新增字段

**workouts 表**:
- `template_source` (JSON): 存储模板来源信息

```json
{
  "template_id": 123,
  "template_name": "上肢训练",
  "applied_at": "2024-12-19T10:30:00",
  "template_description": "专注于上肢肌肉群的综合训练",
  "template_estimated_duration": 60,
  "template_target_body_parts": [1, 2, 3],
  "template_training_scenario": "gym"
}
```

### 数据库迁移

运行以下命令应用数据库迁移：

```bash
# 生成迁移文件（如果需要）
alembic revision --autogenerate -m "add template_source to workout"

# 应用迁移
alembic upgrade head
```

## 注意事项

1. **权限控制**: 用户只能应用自己的训练模板
2. **数据完整性**: 应用模板时会自动创建完整的训练数据链
3. **模板追踪**: 通过 `template_source` 字段可以追溯训练日的模板来源
4. **灵活性**: 支持独立使用或关联到训练计划
5. **组记录**: 自动根据模板配置创建组记录，未完成状态

## 错误处理

常见错误及解决方案：

- **400 - 日期格式错误**: 确保日期格式为 YYYY-MM-DD
- **404 - 模板不存在**: 检查模板ID是否正确且用户有权限访问
- **500 - 创建失败**: 检查模板数据完整性，确保包含有效的训练动作

## 后续扩展

该功能为后续功能提供了基础：

1. **批量应用**: 支持一次应用模板到多个日期
2. **自动调度**: 根据训练计划自动应用模板
3. **进度追踪**: 基于模板来源分析训练进展
4. **模板优化**: 根据实际训练数据优化模板 