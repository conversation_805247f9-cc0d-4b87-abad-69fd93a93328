from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, SmallInteger, JSON, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class WorkoutStatus:
    """训练状态常量"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"

class Workout(Base):
    """训练日模型，代表训练计划中的一天训练安排"""
    __tablename__ = "workouts"

    id = Column(Integer, primary_key=True, index=True)
    training_plan_id = Column(Integer, ForeignKey("training_plans.id"), nullable=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)  # 新增：用于独立训练日的用户关联
    name = Column(String(100), nullable=False)  # 例如 "第一天：上肢训练"
    day_of_week = Column(SmallInteger, nullable=True)  # 1-7 或 null 表示灵活安排
    day_number = Column(SmallInteger, nullable=False)  # 在计划中的第几天
    description = Column(Text, nullable=True)
    estimated_duration = Column(SmallInteger, nullable=True)  # 预计时长（分钟）
    target_body_parts = Column(ARRAY(Integer), nullable=True)  # 目标训练部位ID列表
    training_scenario = Column(String(20), nullable=True)  # 训练场景：home, gym 等

    # 添加计划日期字段
    scheduled_date = Column(DateTime(timezone=True), nullable=True, index=True)  # 计划执行日期

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 新增字段
    status = Column(String(20), nullable=False, default=WorkoutStatus.NOT_STARTED)
    actual_duration = Column(SmallInteger, nullable=True)  # 实际训练时长（分钟）
    net_duration = Column(SmallInteger, nullable=True)  # 净训练时长（分钟，不包括暂停时间）
    start_time = Column(DateTime(timezone=True), nullable=True)  # 开始训练时间
    end_time = Column(DateTime(timezone=True), nullable=True)  # 结束训练时间
    last_status_change = Column(DateTime(timezone=True), nullable=True)  # 最后状态变更时间

    # 存储暂停记录 [{"pause_start": "时间戳", "pause_end": "时间戳", "duration": 分钟数}, ...]
    pause_records = Column(JSON, default=list)

    # 记录状态变化历史 [{"status": "状态", "timestamp": "时间戳", "duration": 分钟数}, ...]
    status_history = Column(JSON, default=list)

    # 新增：模板来源追踪字段
    template_source = Column(JSON, nullable=True)  # 存储模板来源信息：{"template_id": xx, "template_name": "xxx", "applied_at": "时间戳", "template_version": "xxx"}
    
    # 新增：训练总结数据
    summary = Column(JSON, nullable=True)  # 存储训练完成后的统计摘要数据

    # 关系
    training_plan = relationship("TrainingPlan", back_populates="workouts")
    workout_exercises = relationship("WorkoutExercise", back_populates="workout", cascade="all, delete-orphan", order_by="WorkoutExercise.order")
    
    # 与DailyWorkout的关系
    daily_workout = relationship("DailyWorkout", back_populates="workout", uselist=False)

    # 与团队训练会话的关系（如果有）
    training_session_id = Column(Integer, ForeignKey("training_sessions.id"), nullable=True)
    training_session = relationship("TrainingSession", foreign_keys=[training_session_id])

    def __repr__(self):
        return f"<Workout {self.id}: {self.name} - {self.status}>"
        
    def create_daily_summary(self, user_id, title=None, content=None, visibility="Everyone"):
        """从Workout创建DailyWorkout摘要"""
        from app.models.daily_workout import DailyWorkout
        from datetime import datetime
        
        # 计算总组数和训练量
        total_sets = 0
        total_volume = 0
        
        # 遍历训练动作计算统计数据
        for ex in self.workout_exercises:
            if ex.sets:
                total_sets += ex.sets
                
            # 计算总重量逻辑 - 如果有相关数据的话
            # 这里需要根据实际情况设计计算逻辑
        
        return DailyWorkout(
            user_id=user_id,
            workout_id=self.id,
            name=self.name,
            title=title or self.name,
            content=content or self.description,
            training_duration=self.actual_duration,
            training_volume=f"{total_volume} kg" if total_volume > 0 else None,
            training_sets=total_sets,
            training_date=self.end_time or datetime.utcnow(),
            visibility=visibility
        )