from typing import Any, List, Dict
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app import crud, models, schemas
from app.api import deps
from app.schemas.workout import (
    WorkoutStatusEnum, WorkoutStatusUpdate, WorkoutStatistics,
    WorkoutWithExercises, WorkoutFullUpdate, WorkoutCreateFull, WorkoutCreateResponse
)
from app.services.workout_service import WorkoutService
import logging
router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/create", response_model=Dict[str, Any], status_code=201)
async def create_workout_with_exercises(
    *,
    db: Session = Depends(deps.get_db),
    workout_data: WorkoutCreateFull,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    创建完整的训练日，包括训练动作和组记录

    **功能说明：**
    - 创建训练日基本信息
    - 创建嵌套的训练动作
    - 创建每个训练动作的组记录
    - 自动忽略所有传入的ID字段，生成新的数据库ID
    - 支持独立训练日和训练计划关联的训练日

    **数据处理：**
    - 忽略 workout_exercises[].id 和 workout_exercises[].workout_id
    - 忽略 set_records[].id 和 set_records[].workout_exercise_id
    - 保留 exercise_id 字段（引用现有动作）
    - 自动设置外键关联关系

    **权限验证：**
    - 验证用户身份
    - 如果提供了 training_plan_id，验证用户是否有权访问该训练计划
    - 验证 exercise_id 是否存在

    **事务处理：**
    - 使用数据库事务确保数据一致性
    - 创建失败时自动回滚

    **响应数据：**
    - 返回完整的创建后数据，包含新生成的所有ID
    - 包含训练动作详情和组记录
    """
    try:
        # 转换 Pydantic 模型为字典
        workout_dict = workout_data.model_dump()

        # 创建训练日
        workout_service = WorkoutService(db)
        created_workout = workout_service.create_workout_with_exercises(
            workout_data=workout_dict,
            user_id=current_user.id
        )

        logger.info(f"用户 {current_user.id} 成功创建训练日 {created_workout}")
        return created_workout

    except ValueError as e:
        # 处理业务逻辑错误（如exercise_id不存在）
        logger.error(f"创建训练日失败 - 业务逻辑错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

    except HTTPException:
        # 重新抛出HTTP异常
        raise

    except Exception as e:
        # 处理其他未预期的错误
        logger.error(f"创建训练日失败 - 系统错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="创建训练日时发生系统错误，请稍后重试"
        )
@router.put("/{workout_id}/status", response_model=schemas.workout.WorkoutInDB)
async def update_workout_status(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    status_update: WorkoutStatusUpdate,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """更新训练状态"""
    # 检查训练日是否存在并属于当前用户
    workout = crud.crud_workout.get(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    # 权限检查：兼容training_plan_id为空和不为空的情况
    if workout.training_plan_id:
        # 有训练计划的训练日：通过训练计划检查权限
        training_plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)
        if not training_plan or training_plan.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")
    else:
        # 独立训练日：直接检查user_id
        if workout.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")

    # 更新状态
    workout_service = WorkoutService(db)
    updated_workout = workout_service.update_workout_status(workout_id, status_update.status)

    return updated_workout

@router.get("/{workout_id}/statistics", response_model=WorkoutStatistics)
async def get_workout_statistics(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """获取训练统计数据"""
    # 检查训练日是否存在并属于当前用户
    workout = crud.crud_workout.get(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    # 权限检查：兼容training_plan_id为空和不为空的情况
    if workout.training_plan_id:
        # 有训练计划的训练日：通过训练计划检查权限
        training_plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)
        if not training_plan or training_plan.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")
    else:
        # 独立训练日：直接检查user_id
        if workout.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")

    # 获取统计数据
    workout_service = WorkoutService(db)
    stats = workout_service.calculate_workout_statistics(workout_id)

    return stats

@router.post("/{workout_id}/link-session/{session_id}", response_model=schemas.workout.WorkoutInDB)
async def link_with_training_session(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    session_id: int,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """关联训练日与团队训练会话"""
    # 检查训练日是否存在并属于当前用户
    workout = crud.crud_workout.get(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    # 权限检查：兼容training_plan_id为空和不为空的情况
    if workout.training_plan_id:
        # 有训练计划的训练日：通过训练计划检查权限
        training_plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)
        if not training_plan or training_plan.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")
    else:
        # 独立训练日：直接检查user_id
        if workout.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")

    # 检查是否有权访问该训练会话
    # TODO: 根据业务逻辑验证用户是否有权关联特定的训练会话
    # 此处简化处理，仅检查基本权限

    # 关联会话
    workout_service = WorkoutService(db)
    updated_workout = workout_service.link_with_training_session(workout_id, session_id)

    return updated_workout

@router.get("/by-date/{date}", response_model=List[Dict[str, Any]])
async def get_workouts_by_date(
    *,
    db: Session = Depends(deps.get_db),
    date: str,
    current_user: models.User = Depends(deps.get_current_active_user),
    brief: bool = Query(False, description="是否只返回简略信息，不包括训练动作和组记录")
) -> Any:
    """根据日期获取训练日及其相关数据

    Args:
        date: 日期，格式为YYYY-MM-DD
        brief: 是否只返回简略信息，不包括训练动作和组记录

    Returns:
        训练日列表，根据brief参数决定是否包含训练动作和组记录
    """
    try:
        # 解析日期字符串
        parsed_date = datetime.strptime(date, "%Y-%m-%d")
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")

    # 获取训练日
    workout_service = WorkoutService(db)

    if brief:
        # 获取简略信息
        workouts = workout_service.get_workouts_brief_by_date(parsed_date, current_user.id)
    else:
        # 获取完整信息
        workouts = workout_service.get_workouts_by_date(parsed_date, current_user.id)
    logger.info(f"获取训练日列表: {workouts}")
    return workouts

@router.get("/{workout_id}", response_model=Dict[str, Any])
async def get_workout_detail(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """获取训练日详情

    Args:
        workout_id: 训练日ID

    Returns:
        训练日详情，包括训练动作
    """
    # 获取训练日详情
    workout = crud.crud_workout.get_with_exercises(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    # 验证用户权限（只能查看自己的训练日）
    if workout["training_plan_id"]:
        # 有训练计划的训练日：通过训练计划检查权限
        training_plan = crud.crud_training_plan.get(db, id=workout["training_plan_id"])
        if not training_plan or training_plan.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")
    else:
        # 独立训练日：通过workout中的user_id检查权限
        # 需要重新获取workout对象以获取user_id
        workout_obj = crud.crud_workout.get(db, id=workout_id)
        if not workout_obj or workout_obj.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")

    return workout

@router.put("/{workout_id}/update-full", response_model=Dict[str, Any])
async def update_workout_full(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    update_data: WorkoutFullUpdate,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """全面更新训练日，包括基本信息、训练动作和组记录
    
    Args:
        workout_id: 训练日ID
        update_data: 更新数据，包含基本信息、训练动作和组记录
        
    Returns:
        更新后的训练日详情，包含训练动作和组记录
    """
    # 检查训练日是否存在并属于当前用户
    workout = crud.crud_workout.get(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    # 权限检查：兼容training_plan_id为空和不为空的情况
    if workout.training_plan_id:
        # 有训练计划的训练日：通过训练计划检查权限
        training_plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)
        if not training_plan or training_plan.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")
    else:
        # 独立训练日：直接检查user_id
        if workout.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此训练日")

    # 调用服务方法执行更新
    workout_service = WorkoutService(db)
    updated_workout = workout_service.update_workout_full(workout_id, update_data)

    return updated_workout

@router.get("/statistics/period", response_model=Dict[str, Any])
async def period_statistic(
    *,
    db: Session = Depends(deps.get_db),
    start_date: str = Query(..., description="开始日期，格式为YYYY-MM-DD"),
    end_date: str = Query(None, description="结束日期，格式为YYYY-MM-DD，默认为当天"),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """获取时间段内训练统计数据，包含环比分析
    
    Args:
        start_date: 开始日期，格式为YYYY-MM-DD
        end_date: 结束日期，格式为YYYY-MM-DD，默认为当天
        
    Returns:
        包含当前周期、对比周期和环比分析的统计数据
    """
    try:
        # 解析开始日期
        parsed_start_date = datetime.strptime(start_date, "%Y-%m-%d")
        
        # 解析结束日期
        parsed_end_date = None
        if end_date:
            parsed_end_date = datetime.strptime(end_date, "%Y-%m-%d")
        
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")
    
    # 获取统计数据
    workout_service = WorkoutService(db)
    statistics = workout_service.period_statistic(
        user_id=current_user.id,
        start_date=parsed_start_date,
        end_date=parsed_end_date
    )
    
    return statistics