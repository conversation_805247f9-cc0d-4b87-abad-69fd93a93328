from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from app.db.session import get_db
from app.models.user import User
from app.models.training_template import WorkoutTemplate
from app.models.workout_exercise import WorkoutExercise
from app.schemas.training_plan import TrainingTemplateCreate
from app.schemas.workout import ApplyTemplateRequest, ApplyTemplateResponse
from app.api.deps import get_current_user
from app.services.training_template_service import TrainingTemplateService
from app.services.set_record_manager import SetRecordManager
from app.crud.crud_workout import CRUDWorkout

router = APIRouter()

# Get logger for this module
logger = logging.getLogger(__name__)

@router.get("/", response_model=List[dict])
def get_workout_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的所有训练模板，包含详细的运动信息和组记录
    """
    try:
        # 使用训练模板服务获取数据
        template_service = TrainingTemplateService(db)
        templates = template_service.get_user_templates_with_details(current_user.id)

        return [template.to_dict() for template in templates]

    except Exception as e:
        logger.error(f"获取训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取训练模板失败: {str(e)}")

@router.get("/{template_id}", response_model=dict)
def get_workout_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取特定训练模板的详情，包含详细的运动信息和组记录
    """
    try:
        # 使用训练模板服务获取数据
        template_service = TrainingTemplateService(db)
        template = template_service.get_template_with_details(template_id, current_user.id)

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        return template.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取训练模板详情失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取训练模板详情失败: {str(e)}")

@router.put("/{template_id}", response_model=dict)
def update_workout_template(
    template_id: int,
    template_data: TrainingTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新训练模板 - 支持差量更新

    参数:
    - template_id: 模板ID
    - template_data: 更新的模板数据

    功能:
    - 自动检测字段变更，只更新实际改变的字段
    - 支持更新模板基本信息和训练动作
    - 维护数据完整性
    """
    try:
        logger.info(f"收到更新训练模板请求，模板ID: {template_id}, 用户ID: {current_user.id}")
        logger.debug(f"更新数据: {template_data}")

        # 使用训练模板服务更新模板
        template_service = TrainingTemplateService(db)
        result = template_service.update_template(template_id, template_data, current_user.id)

        if not result:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新训练模板失败: {str(e)}")

@router.post("/", response_model=dict)
def create_workout_template(
    template_data: TrainingTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的训练模板

    参数:
    - name: 模板名称
    - description: 描述(可选)
    - estimated_duration: 预计时长(可选)
    - target_body_parts: 目标训练部位(可选)
    - training_scenario: 训练场景(可选)
    - exercises: 训练动作信息数组
    - notes: 备注(可选)
    """
    try:
        logger.info(f"收到创建训练模板请求，用户ID: {current_user.id}")
        logger.debug(f"模板数据: {template_data.model_dump()}")

        # 使用训练模板服务创建模板
        template_service = TrainingTemplateService(db)
        created_template = template_service.create_template(template_data, current_user.id)

        if not created_template:
            raise HTTPException(status_code=500, detail="创建训练模板失败")

        logger.info(f"成功创建训练模板，ID: {created_template.id}, 包含 {len(template_data.exercises)} 个动作")
        return created_template.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建训练模板失败: {str(e)}")

@router.delete("/{template_id}")
def delete_workout_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除训练模板
    """
    try:
        template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权删除")

        db.delete(template)  # 级联删除会自动删除关联的 WorkoutExercise
        db.commit()

        return {"message": "训练模板已删除"}

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除训练模板失败: {str(e)}")

@router.post("/{template_id}/apply", response_model=ApplyTemplateResponse)
def apply_workout_template(
    template_id: int,
    date: Optional[str] = Query(None),
    training_plan_id: Optional[int] = Query(None, description="训练计划ID（可选）"),
    body: Optional[dict] = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    应用训练模板到指定日期，创建完整的 Workout、WorkoutExercise 和 SetRecord 记录

    参数:
    - template_id: 模板ID
    - date: 应用日期(YYYY-MM-DD)，可以作为查询参数或请求体参数提供
    - training_plan_id: 训练计划ID（可选，如果workout属于某个训练计划）
    
    返回:
    - workout_id: 创建的训练日ID
    - workout: 训练日详细信息
    - exercises: 创建的训练动作列表
    """
    try:
        # 从查询参数或请求体中获取日期
        date_value = date
        plan_id = training_plan_id
        
        if not date_value and body and "date" in body:
            date_value = body.get("date")
        if not plan_id and body and "training_plan_id" in body:
            plan_id = body.get("training_plan_id")

        if not date_value:
            raise HTTPException(status_code=400, detail="缺少必要参数: date")

        # 验证日期格式并转换为datetime对象
        try:
            from datetime import datetime
            target_datetime = datetime.strptime(date_value, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")

        # 使用训练模板服务应用模板
        template_service = TrainingTemplateService(db)
        result = template_service.apply_workout_template(
            template_id=template_id,
            user_id=current_user.id,
            scheduled_date=target_datetime,
            training_plan_id=plan_id
        )

        if not result:
            raise HTTPException(status_code=404, detail="训练模板不存在、无权访问或应用失败")

        logger.info(f"成功应用训练模板 {template_id} 到日期 {date_value}，创建训练日 {result['workout_id']}")
        return {
            "message": "训练模板应用成功",
            "workout_id": result["workout_id"],
            "workout": result["workout"],
            "exercises": result["exercises"],
            "applied_date": date_value
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"应用训练模板失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"应用训练模板失败: {str(e)}")

@router.get("/{template_id}/workouts", response_model=List[dict])
def get_workouts_from_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取从指定模板创建的所有训练日记录
    
    参数:
    - template_id: 模板ID
    
    返回:
    - 从该模板创建的训练日记录列表
    """
    try:
        # 验证模板存在且用户有权限访问
        template_service = TrainingTemplateService(db)
        template = template_service.get_template_with_details(template_id, current_user.id)
        
        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")
        
        # 获取从该模板创建的训练日记录
        from app.crud.crud_workout import crud_workout
        workouts = crud_workout.get_workouts_from_template(db, template_id, current_user.id)
        
        # 构建返回数据
        result = []
        for workout in workouts:
            workout_dict = {
                "id": workout.id,
                "name": workout.name,
                "description": workout.description,
                "scheduled_date": workout.scheduled_date.isoformat() if workout.scheduled_date else None,
                "status": workout.status,
                "estimated_duration": workout.estimated_duration,
                "actual_duration": workout.actual_duration,
                "target_body_parts": workout.target_body_parts,
                "training_scenario": workout.training_scenario,
                "template_source": workout.template_source,
                "exercise_count": len(workout.workout_exercises) if workout.workout_exercises else 0,
                "created_at": workout.created_at.isoformat() if workout.created_at else None,
                "updated_at": workout.updated_at.isoformat() if workout.updated_at else None
            }
            result.append(workout_dict)
        
        logger.info(f"获取模板 {template_id} 创建的训练日记录，共 {len(result)} 条")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模板训练日记录失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取模板训练日记录失败: {str(e)}")