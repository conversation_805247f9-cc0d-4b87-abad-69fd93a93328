"""
增强聊天API端点

整合所有优化组件的统一API接口：
- EnhancedLangGraphService
- WebSocket优化器
- 高级缓存优化器
- 渐进式参数收集器
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from typing import Dict, Any, Optional, List
from datetime import datetime
import asyncio
import json

from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import get_async_db

# 导入优化组件
from app.services.ai_assistant.integration.enhanced_langgraph_service import EnhancedLangGraphService
from app.services.ai_assistant.integration.websocket_optimizer import websocket_optimizer
from app.services.ai_assistant.integration.advanced_cache_optimizer import AdvancedCacheOptimizer
from app.services.ai_assistant.integration.progressive_parameter_collector import ProgressiveParameterCollector

# 导入现有服务
from app.services.ai_assistant.llm.llm_proxy_service import LLMProxyService
from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState

from app.core.config import settings
from app.services.ai_assistant.logger.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 全局服务实例
enhanced_services: Dict[str, Any] = {}


async def get_enhanced_services(db: AsyncSession = Depends(get_async_db)):
    """获取增强服务实例"""
    session_id = id(db)  # 使用数据库会话ID作为标识
    
    if session_id not in enhanced_services:
        # 初始化服务
        llm_service = LLMProxyService()
        
        enhanced_services[session_id] = {
            "langgraph_service": EnhancedLangGraphService(db, llm_service),
            "cache_optimizer": AdvancedCacheOptimizer(db, settings.REDIS_URL),
            "parameter_collector": ProgressiveParameterCollector(db, llm_service)
        }
        
        logger.info(f"增强服务实例已创建: {session_id}")
    
    return enhanced_services[session_id]


@router.post("/chat/enhanced")
async def enhanced_chat(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    services: Dict = Depends(get_enhanced_services)
) -> Dict[str, Any]:
    """增强聊天接口"""
    try:
        # 提取请求参数
        user_message = request.get("message", "")
        user_id = request.get("user_id", "anonymous")
        session_id = request.get("session_id", f"session_{user_id}_{int(datetime.now().timestamp())}")
        workflow_mode = request.get("workflow_mode", "enhanced")
        
        if not user_message:
            raise HTTPException(status_code=400, detail="消息内容不能为空")
        
        # 构建状态
        state = UnifiedFitnessState(
            user_id=user_id,
            session_id=session_id,
            conversation_id=session_id,
            user_message=user_message,
            timestamp=datetime.now().isoformat()
        )
        
        # 缓存检查
        cache_key = f"chat:{user_id}:{hash(user_message)}"
        cached_response = await services["cache_optimizer"].get(cache_key)
        
        if cached_response:
            logger.info(f"返回缓存响应: {cache_key}")
            return {
                "success": True,
                "response": cached_response,
                "cached": True,
                "timestamp": datetime.now().isoformat()
            }
        
        # 使用增强LangGraph服务处理
        result = await services["langgraph_service"].process_request(
            state=state,
            workflow_mode=workflow_mode
        )
        
        # 缓存响应
        background_tasks.add_task(
            services["cache_optimizer"].set,
            cache_key,
            result,
            ttl=3600  # 1小时缓存
        )
        
        return {
            "success": True,
            "response": result,
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"增强聊天处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


@router.get("/chat/stream/{session_id}")
async def stream_chat(
    session_id: str,
    message: str,
    user_id: str = "anonymous",
    workflow_mode: str = "streaming",
    services: Dict = Depends(get_enhanced_services)
):
    """流式聊天接口"""
    
    async def generate_stream():
        try:
            # 构建状态
            state = UnifiedFitnessState(
                user_id=user_id,
                session_id=session_id,
                conversation_id=session_id,
                user_message=message,
                timestamp=datetime.now().isoformat()
            )
            
            # 流式处理
            async for chunk in services["langgraph_service"].stream_process_request(
                state=state,
                config={"workflow_mode": workflow_mode}
            ):
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                
        except Exception as e:
            error_chunk = {
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@router.websocket("/chat/ws/{user_id}")
async def websocket_chat(
    websocket: WebSocket,
    user_id: str,
    services: Dict = Depends(get_enhanced_services)
):
    """WebSocket聊天接口"""
    connection_id = f"ws_{user_id}_{int(datetime.now().timestamp())}"
    
    try:
        # 添加到WebSocket优化器
        manager = await websocket_optimizer.add_connection(
            connection_id=connection_id,
            websocket=websocket,
            group=f"user_{user_id}"
        )
        
        logger.info(f"WebSocket连接建立: {connection_id}")
        
        # 发送欢迎消息
        await manager.send_message({
            "type": "welcome",
            "message": "欢迎使用智能健身助手！",
            "connection_id": connection_id,
            "timestamp": datetime.now().isoformat()
        })
        
        # 消息处理循环
        while True:
            # 接收消息
            message_data = await manager.receive_message()
            
            if message_data is None:
                break
            
            try:
                # 解析消息
                message_type = message_data.get("type", "chat")
                
                if message_type == "chat":
                    await handle_chat_message(message_data, user_id, manager, services)
                elif message_type == "parameter_collection":
                    await handle_parameter_collection(message_data, user_id, manager, services)
                elif message_type == "ping":
                    await manager.send_message({"type": "pong", "timestamp": datetime.now().isoformat()})
                else:
                    await manager.send_message({
                        "type": "error",
                        "error": f"未知消息类型: {message_type}",
                        "timestamp": datetime.now().isoformat()
                    })
                    
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {str(e)}")
                await manager.send_message({
                    "type": "error",
                    "error": f"消息处理失败: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                })
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {connection_id}")
    except Exception as e:
        logger.error(f"WebSocket错误: {connection_id}, 错误: {str(e)}")
    finally:
        # 清理连接
        await websocket_optimizer.remove_connection(connection_id)


async def handle_chat_message(
    message_data: Dict[str, Any],
    user_id: str,
    manager,
    services: Dict
):
    """处理聊天消息"""
    try:
        user_message = message_data.get("message", "")
        session_id = message_data.get("session_id", f"ws_{user_id}")
        
        # 构建状态
        state = UnifiedFitnessState(
            user_id=user_id,
            session_id=session_id,
            conversation_id=session_id,
            user_message=user_message,
            timestamp=datetime.now().isoformat()
        )
        
        # 发送处理中状态
        await manager.send_message({
            "type": "processing",
            "message": "正在处理您的消息...",
            "timestamp": datetime.now().isoformat()
        })
        
        # 流式处理
        async for chunk in services["langgraph_service"].stream_process_request(state):
            await manager.send_message({
                "type": "stream_chunk",
                "data": chunk,
                "timestamp": datetime.now().isoformat()
            })
        
        # 发送完成信号
        await manager.send_message({
            "type": "chat_complete",
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        await manager.send_message({
            "type": "error",
            "error": f"聊天处理失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        })


async def handle_parameter_collection(
    message_data: Dict[str, Any],
    user_id: str,
    manager,
    services: Dict
):
    """处理参数收集"""
    try:
        action = message_data.get("action", "start")
        
        if action == "start":
            # 开始参数收集
            result = await services["parameter_collector"].start_collection_session(
                user_id=user_id,
                state=UnifiedFitnessState(user_id=user_id, session_id=f"collect_{user_id}")
            )
            
            await manager.send_message({
                "type": "parameter_collection_start",
                "data": result,
                "timestamp": datetime.now().isoformat()
            })
            
        elif action == "answer":
            # 回答参数问题
            parameter = message_data.get("parameter")
            response = message_data.get("response")
            
            result = await services["parameter_collector"].collect_parameter_interactively(
                user_id=user_id,
                parameter=parameter,
                user_response=response
            )
            
            await manager.send_message({
                "type": "parameter_collection_response",
                "data": result,
                "timestamp": datetime.now().isoformat()
            })
            
        elif action == "skip":
            # 跳过可选参数
            result = await services["parameter_collector"].collect_parameter_interactively(
                user_id=user_id
            )
            
            await manager.send_message({
                "type": "parameter_collection_skip",
                "data": result,
                "timestamp": datetime.now().isoformat()
            })
            
    except Exception as e:
        await manager.send_message({
            "type": "error",
            "error": f"参数收集失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        })


@router.get("/chat/stats")
async def get_chat_stats(services: Dict = Depends(get_enhanced_services)) -> Dict[str, Any]:
    """获取聊天统计信息"""
    try:
        # WebSocket统计
        websocket_stats = websocket_optimizer.get_global_stats()
        
        # 缓存统计
        cache_stats = services["cache_optimizer"].get_optimization_stats()
        
        # 参数收集统计
        collection_stats = services["parameter_collector"].get_collection_stats()
        
        # 服务健康检查
        health_check = await services["langgraph_service"].health_check()
        
        return {
            "websocket": websocket_stats,
            "cache": cache_stats,
            "parameter_collection": collection_stats,
            "service_health": health_check,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.post("/chat/config")
async def update_chat_config(
    config: Dict[str, Any],
    services: Dict = Depends(get_enhanced_services)
) -> Dict[str, Any]:
    """更新聊天配置"""
    try:
        # 更新LangGraph服务配置
        if "langgraph" in config:
            await services["langgraph_service"].configure_service(config["langgraph"])
        
        # 更新缓存配置
        if "cache" in config:
            # 缓存配置更新逻辑
            pass
        
        return {
            "success": True,
            "message": "配置更新成功",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"配置更新失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"配置更新失败: {str(e)}")


@router.get("/chat/health")
async def health_check(services: Dict = Depends(get_enhanced_services)) -> Dict[str, Any]:
    """健康检查接口"""
    try:
        # 检查各个服务状态
        langgraph_health = await services["langgraph_service"].health_check()
        
        # WebSocket连接状态
        websocket_health = {
            "healthy": True,
            "active_connections": websocket_optimizer.global_metrics["active_connections"],
            "total_connections": websocket_optimizer.global_metrics["total_connections"]
        }
        
        # 缓存状态
        cache_stats = services["cache_optimizer"].get_optimization_stats()
        cache_health = {
            "healthy": True,
            "l1_hit_rate": cache_stats.get("l1_memory", {}).get("hit_rate", 0),
            "total_entries": sum(level.get("entries_count", 0) for level in cache_stats.values() if isinstance(level, dict))
        }
        
        overall_healthy = (
            langgraph_health.get("healthy", False) and
            websocket_health.get("healthy", False) and
            cache_health.get("healthy", False)
        )
        
        return {
            "healthy": overall_healthy,
            "services": {
                "langgraph": langgraph_health,
                "websocket": websocket_health,
                "cache": cache_health
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        } 