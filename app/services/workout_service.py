from datetime import datetime
from typing import Optional, List, Dict, Any
from fastapi import HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.models.workout import Workout, WorkoutStatus
from app.models.workout_exercise import WorkoutExercise
from app.models.set_record import SetRecord
from app.models.exercise import Exercise, ExerciseDetail
from app.schemas.workout import WorkoutStatusEnum, WorkoutFullUpdate
from app.models.team.training import TrainingSession

class WorkoutService:
    def __init__(self, db: Session):
        self.db = db

    def update_workout_status(self, workout_id: int, status: WorkoutStatusEnum) -> Workout:
        """更新训练状态，处理状态变更逻辑、计算时间"""
        workout = self.db.query(Workout).filter(Workout.id == workout_id).first()
        if not workout:
            raise HTTPException(status_code=404, detail="Workout not found")

        # 获取当前时间
        now = datetime.now()

        # 计算上次状态持续时间（如果有上次状态）
        previous_status = workout.status
        previous_duration = None
        if workout.last_status_change:
            previous_duration = int((now - workout.last_status_change).total_seconds() / 60)

        # 添加状态历史记录
        if not workout.status_history:
            workout.status_history = []

        if workout.last_status_change:
            workout.status_history.append({
                "status": previous_status,
                "timestamp": workout.last_status_change.isoformat(),
                "duration": previous_duration
            })

        # 处理特定状态转换逻辑
        if previous_status == WorkoutStatus.NOT_STARTED and status == WorkoutStatusEnum.IN_PROGRESS:
            # 首次开始训练
            workout.start_time = now

        elif previous_status == WorkoutStatus.IN_PROGRESS and status == WorkoutStatusEnum.PAUSED:
            # 暂停训练
            if not workout.pause_records:
                workout.pause_records = []

            workout.pause_records.append({
                "pause_start": now.isoformat(),
                "pause_end": None,
                "duration": None
            })

        elif previous_status == WorkoutStatus.PAUSED and status == WorkoutStatusEnum.IN_PROGRESS:
            # 恢复训练
            if workout.pause_records and len(workout.pause_records) > 0:
                last_pause = workout.pause_records[-1]
                if last_pause.get("pause_end") is None:
                    pause_duration = int((now - datetime.fromisoformat(last_pause["pause_start"])).total_seconds() / 60)
                    last_pause["pause_end"] = now.isoformat()
                    last_pause["duration"] = pause_duration
                    workout.pause_records[-1] = last_pause

        elif status == WorkoutStatusEnum.COMPLETED:
            # 完成训练
            workout.end_time = now

            # 关闭任何未结束的暂停
            if workout.pause_records and len(workout.pause_records) > 0:
                last_pause = workout.pause_records[-1]
                if last_pause.get("pause_end") is None:
                    pause_duration = int((now - datetime.fromisoformat(last_pause["pause_start"])).total_seconds() / 60)
                    last_pause["pause_end"] = now.isoformat()
                    last_pause["duration"] = pause_duration
                    workout.pause_records[-1] = last_pause

            # 计算总训练时间
            if workout.start_time:
                workout.actual_duration = int((now - workout.start_time).total_seconds() / 60)

                # 计算总暂停时间
                total_pause_time = 0
                if workout.pause_records:
                    for pause in workout.pause_records:
                        if pause.get("duration"):
                            total_pause_time += pause["duration"]

                # 计算净训练时间（总时间减去暂停时间）
                workout.net_duration = max(0, workout.actual_duration - total_pause_time)

            # 计算并存储训练总结数据
            workout.summary = self._calculate_workout_summary(workout.id)

        # 更新状态和状态变更时间
        workout.status = status
        workout.last_status_change = now

        # 同步团队训练会话状态（如果有）
        if workout.training_session_id:
            self._sync_with_training_session(workout, status)

        self.db.commit()
        self.db.refresh(workout)
        return workout

    def _sync_with_training_session(self, workout: Workout, status: WorkoutStatusEnum) -> None:
        """同步训练日状态到团队训练会话"""
        session = self.db.query(TrainingSession).filter(TrainingSession.id == workout.training_session_id).first()
        if not session:
            return

        # 状态映射（个人训练状态 -> 团队训练会话状态）
        status_mapping = {
            WorkoutStatus.NOT_STARTED: "scheduled",
            WorkoutStatus.IN_PROGRESS: "in_progress",
            WorkoutStatus.PAUSED: "paused",
            WorkoutStatus.COMPLETED: "completed"
        }

        # 更新会话状态
        session.status = status_mapping.get(str(status), session.status)

        # 更新会话开始/结束时间
        if status == WorkoutStatusEnum.IN_PROGRESS and not session.actual_start:
            session.actual_start = datetime.now()
        elif status == WorkoutStatusEnum.COMPLETED and not session.actual_end:
            session.actual_end = datetime.now()

        self.db.add(session)

    def _calculate_workout_summary(self, workout_id: int) -> Dict[str, Any]:
        """计算训练完成后的统计摘要数据
        
        Args:
            workout_id: 训练日ID
            
        Returns:
            包含训练统计数据的字典
        """
        from collections import defaultdict
        
        # 获取训练动作和组记录
        workout_exercises = self.db.query(WorkoutExercise).filter(
            WorkoutExercise.workout_id == workout_id
        ).all()
        
        if not workout_exercises:
            return {}
            
        # 初始化统计数据
        summary = {
            "training_time": {
                "total_duration": 0,  # 总训练时间（分钟）
                "net_duration": 0     # 净训练时间（分钟）
            },
            "exercise_types": {
                "weight_reps": {
                    "exercise_count": 0,
                    "total_sets": 0,
                    "total_reps": 0,
                    "total_volume": 0.0  # kg
                },
                "duration": {
                    "exercise_count": 0,
                    "total_duration": 0  # 秒
                }
            },
            "body_parts": {},  # 按身体部位统计
            "muscles": {
                "target": {},      # 目标肌肉群统计
                "synergist": {}    # 协同肌肉群统计
            },
            "max_weights": {},     # 各动作的最大重量
            "unique_exercises": set()  # 唯一训练动作数量
        }
        
        # 获取训练日的时间信息
        workout = self.db.query(Workout).filter(Workout.id == workout_id).first()
        if workout:
            summary["training_time"]["total_duration"] = workout.actual_duration or 0
            summary["training_time"]["net_duration"] = workout.net_duration or 0
        
        # 获取所有动作的详细信息
        exercise_ids = [we.exercise_id for we in workout_exercises]
        exercises_details = {}
        exercise_detail_map = {}
        if exercise_ids:
            exercises = self.db.query(Exercise).filter(Exercise.id.in_(exercise_ids)).all()
            exercise_details = self.db.query(ExerciseDetail).filter(
                ExerciseDetail.exercise_id.in_(exercise_ids)
            ).all()
            
            exercises_details = {ex.id: ex for ex in exercises}
            exercise_detail_map = {ed.exercise_id: ed for ed in exercise_details}
        
        # 按动作类型处理
        for we in workout_exercises:
            exercise_type = we.exercise_type or "weight_reps"
            exercise_id = we.exercise_id
            
            # 添加到唯一动作集合
            summary["unique_exercises"].add(exercise_id)
            
            # 获取动作详情
            exercise = exercises_details.get(exercise_id)
            exercise_detail = exercise_detail_map.get(exercise_id)
            
            if exercise_type == "weight_reps":
                # 处理重量+次数类型动作
                summary["exercise_types"]["weight_reps"]["exercise_count"] += 1
                
                # 获取组记录
                set_records = self.db.query(SetRecord).filter(
                    SetRecord.workout_exercise_id == we.id,
                    SetRecord.completed == True
                ).all()
                
                for sr in set_records:
                    summary["exercise_types"]["weight_reps"]["total_sets"] += 1
                    summary["exercise_types"]["weight_reps"]["total_reps"] += sr.reps or 0
                    
                    # 计算容量（重量 x 次数）
                    weight = sr.weight or 0
                    reps = sr.reps or 0
                    volume = weight * reps
                    summary["exercise_types"]["weight_reps"]["total_volume"] += volume
                    
                    # 更新最大重量记录
                    if exercise_id not in summary["max_weights"] or weight > summary["max_weights"][exercise_id]["weight"]:
                        summary["max_weights"][exercise_id] = {
                            "weight": weight,
                            "reps": reps,
                            "exercise_name": exercise.name if exercise else f"动作{exercise_id}"
                        }
                
                # 按身体部位统计
                if exercise and exercise.body_part_id:
                    for body_part_id in exercise.body_part_id:
                        if body_part_id not in summary["body_parts"]:
                            summary["body_parts"][body_part_id] = {
                                "sets": 0,
                                "reps": 0,
                                "volume": 0.0
                            }
                        
                        for sr in set_records:
                            summary["body_parts"][body_part_id]["sets"] += 1
                            summary["body_parts"][body_part_id]["reps"] += sr.reps or 0
                            summary["body_parts"][body_part_id]["volume"] += (sr.weight or 0) * (sr.reps or 0)
                
                # 按肌肉群统计
                if exercise_detail:
                    # 目标肌肉群
                    if exercise_detail.target_muscles_id:
                        for muscle_id in exercise_detail.target_muscles_id:
                            if muscle_id not in summary["muscles"]["target"]:
                                summary["muscles"]["target"][muscle_id] = {
                                    "sets": 0,
                                    "reps": 0,
                                    "volume": 0.0
                                }
                            
                            for sr in set_records:
                                summary["muscles"]["target"][muscle_id]["sets"] += 1
                                summary["muscles"]["target"][muscle_id]["reps"] += sr.reps or 0
                                summary["muscles"]["target"][muscle_id]["volume"] += (sr.weight or 0) * (sr.reps or 0)
                    
                    # 协同肌肉群
                    if exercise_detail.synergist_muscles_id:
                        for muscle_id in exercise_detail.synergist_muscles_id:
                            if muscle_id not in summary["muscles"]["synergist"]:
                                summary["muscles"]["synergist"][muscle_id] = {
                                    "sets": 0,
                                    "reps": 0,
                                    "volume": 0.0
                                }
                            
                            for sr in set_records:
                                summary["muscles"]["synergist"][muscle_id]["sets"] += 1
                                summary["muscles"]["synergist"][muscle_id]["reps"] += sr.reps or 0
                                summary["muscles"]["synergist"][muscle_id]["volume"] += (sr.weight or 0) * (sr.reps or 0)
                                
            elif exercise_type == "duration":
                # 处理持续时间类型动作
                summary["exercise_types"]["duration"]["exercise_count"] += 1
                
                # 获取持续时间（从组记录或动作设置中获取）
                set_records = self.db.query(SetRecord).filter(
                    SetRecord.workout_exercise_id == we.id,
                    SetRecord.completed == True
                ).all()
                
                total_duration = 0
                for sr in set_records:
                    # 这里假设duration类型的动作在SetRecord中用reps字段存储秒数
                    # 如果有专门的duration字段，需要调整
                    duration = sr.reps or 0  # 或者使用其他字段存储时长
                    total_duration += duration
                
                summary["exercise_types"]["duration"]["total_duration"] += total_duration
        
        # 转换唯一动作集合为数量
        summary["unique_exercises"] = len(summary["unique_exercises"])
        
        return summary

    def calculate_workout_statistics(self, workout_id: int) -> Dict[str, Any]:
        """计算训练统计数据"""
        workout = self.db.query(Workout).filter(Workout.id == workout_id).first()
        if not workout:
            raise HTTPException(status_code=404, detail="Workout not found")

        result = {
            "workout_id": workout.id,
            "status": workout.status,
            "estimated_duration": workout.estimated_duration,
            "actual_duration": workout.actual_duration,
            "net_duration": workout.net_duration,
            "total_pause_time": 0,
            "pause_count": len(workout.pause_records) if workout.pause_records else 0,
            "time_efficiency": 0  # 净训练时间/总训练时间
        }

        # 计算总暂停时间
        if workout.pause_records:
            for pause in workout.pause_records:
                if pause.get("duration"):
                    result["total_pause_time"] += pause["duration"]

        # 计算时间效率
        if workout.actual_duration and workout.actual_duration > 0:
            result["time_efficiency"] = round((workout.net_duration or 0) / workout.actual_duration * 100, 2)

        return result

    def link_with_training_session(self, workout_id: int, session_id: int) -> Workout:
        """将训练日与团队训练会话关联"""
        workout = self.db.query(Workout).filter(Workout.id == workout_id).first()
        if not workout:
            raise HTTPException(status_code=404, detail="Workout not found")

        session = self.db.query(TrainingSession).filter(TrainingSession.id == session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="Training session not found")

        workout.training_session_id = session_id

        # 同步状态
        if session.status == "in_progress" and workout.status == WorkoutStatus.NOT_STARTED:
            workout.status = WorkoutStatus.IN_PROGRESS
            workout.start_time = session.actual_start or datetime.now()
            workout.last_status_change = datetime.now()

        self.db.commit()
        self.db.refresh(workout)
        return workout

    def get_workouts_by_date(self, date: datetime, user_id: int) -> List[Dict[str, Any]]:
        """根据日期获取用户的所有训练日及其相关数据

        Args:
            date: 日期
            user_id: 用户ID

        Returns:
            包含训练动作和组记录的训练日列表
        """
        from app.crud.crud_workout import CRUDWorkout
        workout_crud = CRUDWorkout(self.db)

        # 获取指定日期的所有训练日
        workouts = workout_crud.get_by_date(self.db, date=date, user_id=user_id)

        result = []
        for w in workouts:
            # 获取训练日详情
            workout_data = {
                "id": w.id,
                "name": w.name,
                "training_plan_id": w.training_plan_id,
                "day_of_week": w.day_of_week,
                "day_number": w.day_number,
                "description": w.description,
                "estimated_duration": w.estimated_duration,
                "scheduled_date": w.scheduled_date,
                "status": w.status,
                "actual_duration": w.actual_duration,
                "net_duration": w.net_duration,
                "start_time": w.start_time,
                "end_time": w.end_time,
                "last_status_change": w.last_status_change,
                "created_at": w.created_at,
                "updated_at": w.updated_at,
                "workout_exercises": []
            }

            # 获取训练动作
            workout_exercises = self.db.query(WorkoutExercise).filter(
                WorkoutExercise.workout_id == w.id
            ).order_by(WorkoutExercise.order).all()

            for we in workout_exercises:
                # 获取动作详情
                exercise = self.db.query(Exercise).filter(Exercise.id == we.exercise_id).first()

                # 获取视频URL
                exercise_detail = self.db.query(ExerciseDetail).filter(ExerciseDetail.exercise_id == we.exercise_id).first()
                video_url = exercise_detail.video_file if exercise_detail else None

                exercise_data = {
                    "id": we.id,
                    "workout_id": we.workout_id,
                    "exercise_id": we.exercise_id,
                    "exercise_name": exercise.name if exercise else f"动作ID: {we.exercise_id}",
                    "exercise_image": exercise.image_name if exercise else None,
                    "exercise_description": exercise.description if exercise else None,
                    "video_url": video_url,
                    "sets": we.sets,
                    "reps": we.reps,
                    "rest_seconds": we.rest_seconds,
                    "order": we.order,
                    "notes": we.notes,
                    "exercise_type": we.exercise_type,
                    "superset_group": we.superset_group,
                    "weight": we.weight,
                    "set_records": []
                }

                # 获取组记录
                set_records = self.db.query(SetRecord).filter(
                    SetRecord.workout_exercise_id == we.id
                ).order_by(SetRecord.set_number).all()

                for sr in set_records:
                    set_record_data = {
                        "id": sr.id,
                        "workout_exercise_id": sr.workout_exercise_id,
                        "set_number": sr.set_number,
                        "set_type": sr.set_type,
                        "weight": sr.weight,
                        "reps": sr.reps,
                        "completed": sr.completed,
                        "notes": sr.notes,
                        "created_at": sr.created_at,
                        "updated_at": sr.updated_at
                    }
                    exercise_data["set_records"].append(set_record_data)

                workout_data["workout_exercises"].append(exercise_data)

            result.append(workout_data)

        return result

    def get_workouts_brief_by_date(self, date: datetime, user_id: int) -> List[Dict[str, Any]]:
        """根据日期获取用户的所有训练日的简略信息，不包括训练动作和组记录

        Args:
            date: 日期
            user_id: 用户ID

        Returns:
            训练日简略信息列表
        """
        from app.crud.crud_workout import CRUDWorkout
        workout_crud = CRUDWorkout(self.db)

        # 获取指定日期的所有训练日
        workouts = workout_crud.get_by_date(self.db, date=date, user_id=user_id)

        result = []
        for w in workouts:
            # 获取训练日简略信息
            workout_data = {
                "id": w.id,
                "name": w.name,
                "training_plan_id": w.training_plan_id,
                "day_of_week": w.day_of_week,
                "day_number": w.day_number,
                "description": w.description,
                "estimated_duration": w.estimated_duration,
                "scheduled_date": w.scheduled_date,
                "status": w.status,
                "actual_duration": w.actual_duration,
                "net_duration": w.net_duration,
                "start_time": w.start_time,
                "end_time": w.end_time,
                "last_status_change": w.last_status_change,
                "created_at": w.created_at,
                "updated_at": w.updated_at,
                # 添加训练动作数量信息
                "exercise_count": self.db.query(WorkoutExercise).filter(
                    WorkoutExercise.workout_id == w.id
                ).count()
            }

            result.append(workout_data)

        return result

    def update_workout_full(self, workout_id: int, update_data: WorkoutFullUpdate) -> Dict[str, Any]:
        """全面更新训练日，包括基本信息、训练动作和组记录
        
        Args:
            workout_id: 训练日ID
            update_data: 更新数据，包含基本信息、训练动作和组记录
            
        Returns:
            更新后的训练日数据，包含训练动作和组记录
        """
        # 1. 获取训练日
        workout = self.db.query(Workout).filter(Workout.id == workout_id).first()
        if not workout:
            raise HTTPException(status_code=404, detail="训练日不存在")
            
        # 2. 更新基本信息
        if update_data.name is not None:
            workout.name = update_data.name
        if update_data.day_of_week is not None:
            workout.day_of_week = update_data.day_of_week
        if update_data.description is not None:
            workout.description = update_data.description
        if update_data.estimated_duration is not None:
            workout.estimated_duration = update_data.estimated_duration
        if update_data.scheduled_date is not None:
            workout.scheduled_date = update_data.scheduled_date
        if update_data.status is not None:
            # 调用现有状态更新方法处理状态变更逻辑
            self.update_workout_status(workout_id, update_data.status)
            
        # 3. 处理训练动作更新
        if update_data.exercises:
            # 获取现有训练动作
            existing_exercises = self.db.query(WorkoutExercise).filter(
                WorkoutExercise.workout_id == workout_id
            ).all()
            existing_exercise_dict = {ex.id: ex for ex in existing_exercises}
            
            # 处理每个训练动作更新
            for exercise_update in update_data.exercises:
                # 3.1 删除训练动作
                if exercise_update.delete and exercise_update.id:
                    if exercise_update.id in existing_exercise_dict:
                        # 删除相关的组记录
                        self.db.query(SetRecord).filter(
                            SetRecord.workout_exercise_id == exercise_update.id
                        ).delete()
                        
                        # 删除训练动作
                        self.db.query(WorkoutExercise).filter(
                            WorkoutExercise.id == exercise_update.id
                        ).delete()
                    continue
                
                # 3.2 更新现有训练动作
                if exercise_update.id and exercise_update.id in existing_exercise_dict:
                    exercise = existing_exercise_dict[exercise_update.id]
                    
                    # 更新属性
                    if exercise_update.sets is not None:
                        exercise.sets = exercise_update.sets
                    if exercise_update.reps is not None:
                        exercise.reps = exercise_update.reps
                    if exercise_update.rest_seconds is not None:
                        exercise.rest_seconds = exercise_update.rest_seconds
                    if exercise_update.order is not None:
                        exercise.order = exercise_update.order
                    if exercise_update.notes is not None:
                        exercise.notes = exercise_update.notes
                    if exercise_update.exercise_type is not None:
                        exercise.exercise_type = exercise_update.exercise_type
                    if exercise_update.superset_group is not None:
                        exercise.superset_group = exercise_update.superset_group
                    if exercise_update.weight is not None:
                        exercise.weight = exercise_update.weight
                        
                    self.db.add(exercise)
                    
                    # 处理组记录更新
                    if exercise_update.set_records:
                        self._process_set_records(exercise.id, exercise_update.set_records)
                        
                # 3.3 创建新训练动作
                elif not exercise_update.id and exercise_update.exercise_id:
                    new_exercise = WorkoutExercise(
                        workout_id=workout_id,
                        exercise_id=exercise_update.exercise_id,
                        sets=exercise_update.sets or 3,
                        reps=exercise_update.reps or "10",
                        rest_seconds=exercise_update.rest_seconds or 60,
                        order=exercise_update.order or 1,
                        notes=exercise_update.notes,
                        exercise_type=exercise_update.exercise_type or "weight_reps",
                        superset_group=exercise_update.superset_group,
                        weight=exercise_update.weight
                    )
                    self.db.add(new_exercise)
                    self.db.flush()  # 确保获取到新创建的ID
                    
                    # 处理组记录创建
                    if exercise_update.set_records:
                        self._process_set_records(new_exercise.id, exercise_update.set_records)
        
        # 4. 保存所有更改
        self.db.commit()
        
        # 5. 返回更新后的完整数据
        return self.get_workout_detail(workout_id)
    
    def _process_set_records(self, workout_exercise_id: int, set_records: List[Any]) -> None:
        """处理组记录更新
        
        Args:
            workout_exercise_id: 训练动作ID
            set_records: 组记录更新列表
        """
        # 获取现有组记录
        existing_records = self.db.query(SetRecord).filter(
            SetRecord.workout_exercise_id == workout_exercise_id
        ).all()
        existing_record_dict = {record.id: record for record in existing_records}
        
        for record_update in set_records:
            # 更新现有记录
            if record_update.id and record_update.id in existing_record_dict:
                record = existing_record_dict[record_update.id]
                
                if record_update.set_number is not None:
                    record.set_number = record_update.set_number
                if record_update.set_type is not None:
                    record.set_type = record_update.set_type
                if record_update.weight is not None:
                    record.weight = record_update.weight
                if record_update.reps is not None:
                    record.reps = record_update.reps
                if record_update.completed is not None:
                    record.completed = record_update.completed
                if record_update.notes is not None:
                    record.notes = record_update.notes
                    
                self.db.add(record)
                
            # 创建新记录
            elif not record_update.id:
                new_record = SetRecord(
                    workout_exercise_id=workout_exercise_id,
                    set_number=record_update.set_number,
                    set_type=record_update.set_type or "normal",
                    weight=record_update.weight,
                    reps=record_update.reps,
                    completed=record_update.completed or False,
                    notes=record_update.notes
                )
                self.db.add(new_record)
    
    def get_workout_detail(self, workout_id: int) -> Dict[str, Any]:
        """获取训练日详情
        
        Args:
            workout_id: 训练日ID
            
        Returns:
            包含训练动作和组记录的训练日详情
        """
        workout = self.db.query(Workout).filter(Workout.id == workout_id).first()
        if not workout:
            raise HTTPException(status_code=404, detail="训练日不存在")
            
        # 构建响应数据
        workout_data = {
            "id": workout.id,
            "name": workout.name,
            "training_plan_id": workout.training_plan_id,
            "day_of_week": workout.day_of_week,
            "day_number": workout.day_number,
            "description": workout.description,
            "estimated_duration": workout.estimated_duration,
            "scheduled_date": workout.scheduled_date,
            "status": workout.status,
            "actual_duration": workout.actual_duration,
            "net_duration": workout.net_duration,
            "start_time": workout.start_time,
            "end_time": workout.end_time,
            "last_status_change": workout.last_status_change,
            "created_at": workout.created_at,
            "updated_at": workout.updated_at,
            "workout_exercises": []
        }

        # 获取训练动作
        workout_exercises = self.db.query(WorkoutExercise).filter(
            WorkoutExercise.workout_id == workout.id
        ).order_by(WorkoutExercise.order).all()

        for we in workout_exercises:
            # 获取动作详情
            exercise = self.db.query(Exercise).filter(Exercise.id == we.exercise_id).first()

            # 获取视频URL
            exercise_detail = self.db.query(ExerciseDetail).filter(ExerciseDetail.exercise_id == we.exercise_id).first()
            video_url = exercise_detail.video_file if exercise_detail else None

            exercise_data = {
                "id": we.id,
                "workout_id": we.workout_id,
                "exercise_id": we.exercise_id,
                "exercise_name": exercise.name if exercise else f"动作ID: {we.exercise_id}",
                "exercise_image": exercise.image_name if exercise else None,
                "exercise_description": exercise.description if exercise else None,
                "video_url": video_url,
                "sets": we.sets,
                "reps": we.reps,
                "rest_seconds": we.rest_seconds,
                "order": we.order,
                "notes": we.notes,
                "exercise_type": we.exercise_type,
                "superset_group": we.superset_group,
                "weight": we.weight,
                "set_records": []
            }

            # 获取组记录
            set_records = self.db.query(SetRecord).filter(
                SetRecord.workout_exercise_id == we.id
            ).order_by(SetRecord.set_number).all()

            for sr in set_records:
                set_record_data = {
                    "id": sr.id,
                    "workout_exercise_id": sr.workout_exercise_id,
                    "set_number": sr.set_number,
                    "set_type": sr.set_type,
                    "weight": sr.weight,
                    "reps": sr.reps,
                    "completed": sr.completed,
                    "notes": sr.notes,
                    "created_at": sr.created_at,
                    "updated_at": sr.updated_at
                }
                exercise_data["set_records"].append(set_record_data)

            workout_data["workout_exercises"].append(exercise_data)

        return workout_data

    def period_statistic(self, user_id: int, start_date: datetime, end_date: datetime = None) -> Dict[str, Any]:
        """时间段内训练统计，包含环比分析
        
        Args:
            user_id: 用户ID
            start_date: 开始时间
            end_date: 结束时间，默认为当天
            
        Returns:
            包含当前周期和对比周期的统计数据
        """
        from datetime import timedelta
        from collections import defaultdict
        
        if end_date is None:
            end_date = datetime.now()
        
        # 计算时间段长度
        period_length = (end_date - start_date).days
        
        # 计算对比周期的时间范围
        comparison_end = start_date - timedelta(days=1)
        comparison_start = comparison_end - timedelta(days=period_length)
        
        # 获取当前周期的数据
        current_stats = self._get_period_statistics(user_id, start_date, end_date)
        
        # 获取对比周期的数据  
        comparison_stats = self._get_period_statistics(user_id, comparison_start, comparison_end)
        
        # 计算环比变化
        comparison_result = self._calculate_period_comparison(current_stats, comparison_stats)
        
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": period_length + 1
            },
            "current_period": current_stats,
            "comparison_period": {
                "start_date": comparison_start.isoformat(),
                "end_date": comparison_end.isoformat(),
                "stats": comparison_stats
            },
            "comparison": comparison_result
        }
    
    def _get_period_statistics(self, user_id: int, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """获取指定时间段的训练统计数据
        
        Args:
            user_id: 用户ID
            start_date: 开始时间
            end_date: 结束时间
            
        Returns:
            时间段内的统计数据
        """
        from collections import defaultdict
        from app.models.training_plan import TrainingPlan
        
        # 查询已完成的训练日（支持有训练计划和独立训练）
        completed_workouts = self.db.query(Workout).outerjoin(
            TrainingPlan,
            Workout.training_plan_id == TrainingPlan.id
        ).filter(
            or_(
                # 情况1：有训练计划的训练日
                and_(
                    Workout.training_plan_id.isnot(None),
                    TrainingPlan.user_id == user_id,
                    TrainingPlan.is_active == True
                ),
                # 情况2：独立训练日
                and_(
                    Workout.training_plan_id.is_(None),
                    Workout.user_id == user_id
                )
            ),
            Workout.status == WorkoutStatus.COMPLETED,
            Workout.scheduled_date >= start_date.date(),
            Workout.scheduled_date <= end_date.date()
        ).all()
        
        # 初始化统计数据
        stats = {
            "training_days": {
                "total": 0,
                "by_type": defaultdict(int)
            },
            "training_time": {
                "total_duration": 0,  # 分钟
                "net_duration": 0,    # 分钟
                "by_type": defaultdict(lambda: {"total": 0, "net": 0})
            },
            "volume": {
                "total_volume": 0.0,  # kg，仅weight_reps类型
                "by_body_part": defaultdict(float),
                "by_muscle": {
                    "target": defaultdict(float),
                    "synergist": defaultdict(float)
                }
            },
            "exercises": {
                "unique_count": set(),
                "by_type": defaultdict(set),
                "max_weights": {}
            },
            "sets_and_reps": {
                "total_sets": 0,
                "total_reps": 0,
                "by_type": defaultdict(lambda: {"sets": 0, "reps": 0})
            }
        }
        
        for workout in completed_workouts:
            # 基本统计
            stats["training_days"]["total"] += 1
            stats["training_time"]["total_duration"] += workout.actual_duration or 0
            stats["training_time"]["net_duration"] += workout.net_duration or 0
            
            # 如果有summary数据，使用summary进行统计
            if workout.summary:
                summary = workout.summary
                
                # 按动作类型统计训练天数
                for exercise_type, type_data in summary.get("exercise_types", {}).items():
                    if type_data.get("exercise_count", 0) > 0:
                        stats["training_days"]["by_type"][exercise_type] += 1
                        
                        # 时间统计
                        stats["training_time"]["by_type"][exercise_type]["total"] += workout.actual_duration or 0
                        stats["training_time"]["by_type"][exercise_type]["net"] += workout.net_duration or 0
                
                # 重量和容量统计（仅weight_reps类型）
                weight_reps_data = summary.get("exercise_types", {}).get("weight_reps", {})
                if weight_reps_data:
                    total_volume = weight_reps_data.get("total_volume", 0)
                    stats["volume"]["total_volume"] += total_volume
                    
                    stats["sets_and_reps"]["total_sets"] += weight_reps_data.get("total_sets", 0)
                    stats["sets_and_reps"]["total_reps"] += weight_reps_data.get("total_reps", 0)
                    stats["sets_and_reps"]["by_type"]["weight_reps"]["sets"] += weight_reps_data.get("total_sets", 0)
                    stats["sets_and_reps"]["by_type"]["weight_reps"]["reps"] += weight_reps_data.get("total_reps", 0)
                
                # 持续时间类型统计
                duration_data = summary.get("exercise_types", {}).get("duration", {})
                if duration_data:
                    stats["sets_and_reps"]["by_type"]["duration"]["sets"] += duration_data.get("exercise_count", 0)
                
                # 身体部位容量统计
                for body_part_id, body_part_data in summary.get("body_parts", {}).items():
                    stats["volume"]["by_body_part"][body_part_id] += body_part_data.get("volume", 0)
                
                # 肌肉群容量统计
                for muscle_id, muscle_data in summary.get("muscles", {}).get("target", {}).items():
                    stats["volume"]["by_muscle"]["target"][muscle_id] += muscle_data.get("volume", 0)
                
                for muscle_id, muscle_data in summary.get("muscles", {}).get("synergist", {}).items():
                    stats["volume"]["by_muscle"]["synergist"][muscle_id] += muscle_data.get("volume", 0)
                
                # 唯一动作统计
                unique_exercises = summary.get("unique_exercises", 0)
                if isinstance(unique_exercises, int):
                    # 如果是数量，需要通过其他方式获取实际动作ID
                    # 这里简化处理，直接累加数量
                    pass
                
                # 最大重量统计
                for exercise_id, weight_data in summary.get("max_weights", {}).items():
                    current_weight = weight_data.get("weight", 0)
                    if exercise_id not in stats["exercises"]["max_weights"] or \
                       current_weight > stats["exercises"]["max_weights"][exercise_id]["weight"]:
                        stats["exercises"]["max_weights"][exercise_id] = weight_data
            
            else:
                # 如果没有summary，实时计算（向后兼容）
                workout_exercises = self.db.query(WorkoutExercise).filter(
                    WorkoutExercise.workout_id == workout.id
                ).all()
                
                exercise_types_in_workout = set()
                for we in workout_exercises:
                    exercise_type = we.exercise_type or "weight_reps"
                    exercise_types_in_workout.add(exercise_type)
                    stats["exercises"]["unique_count"].add(we.exercise_id)
                    stats["exercises"]["by_type"][exercise_type].add(we.exercise_id)
                
                # 统计本次训练包含的动作类型
                for exercise_type in exercise_types_in_workout:
                    stats["training_days"]["by_type"][exercise_type] += 1
                    stats["training_time"]["by_type"][exercise_type]["total"] += workout.actual_duration or 0
                    stats["training_time"]["by_type"][exercise_type]["net"] += workout.net_duration or 0
        
        # 转换集合为数量
        stats["exercises"]["unique_count"] = len(stats["exercises"]["unique_count"])
        for exercise_type in stats["exercises"]["by_type"]:
            stats["exercises"]["by_type"][exercise_type] = len(stats["exercises"]["by_type"][exercise_type])
        
        # 转换defaultdict为普通dict
        stats["training_days"]["by_type"] = dict(stats["training_days"]["by_type"])
        stats["training_time"]["by_type"] = dict(stats["training_time"]["by_type"])
        stats["volume"]["by_body_part"] = dict(stats["volume"]["by_body_part"])
        stats["volume"]["by_muscle"]["target"] = dict(stats["volume"]["by_muscle"]["target"])
        stats["volume"]["by_muscle"]["synergist"] = dict(stats["volume"]["by_muscle"]["synergist"])
        stats["sets_and_reps"]["by_type"] = dict(stats["sets_and_reps"]["by_type"])
        
        return stats
    
    def _calculate_period_comparison(self, current_stats: Dict[str, Any], comparison_stats: Dict[str, Any]) -> Dict[str, Any]:
        """计算两个周期的对比数据
        
        Args:
            current_stats: 当前周期统计
            comparison_stats: 对比周期统计
            
        Returns:
            包含变化百分比的对比数据
        """
        def calculate_change_percentage(current: float, previous: float) -> Dict[str, Any]:
            """计算变化百分比"""
            if previous == 0:
                if current == 0:
                    return {"change": 0, "percentage": 0, "direction": "same"}
                else:
                    return {"change": current, "percentage": float('inf'), "direction": "increase"}
            
            change = current - previous
            percentage = (change / previous) * 100
            direction = "increase" if change > 0 else "decrease" if change < 0 else "same"
            
            return {
                "change": change,
                "percentage": round(percentage, 2),
                "direction": direction
            }
        
        comparison = {
            "training_days": {
                "total": calculate_change_percentage(
                    current_stats["training_days"]["total"],
                    comparison_stats["training_days"]["total"]
                )
            },
            "training_time": {
                "total_duration": calculate_change_percentage(
                    current_stats["training_time"]["total_duration"],
                    comparison_stats["training_time"]["total_duration"]
                ),
                "net_duration": calculate_change_percentage(
                    current_stats["training_time"]["net_duration"],
                    comparison_stats["training_time"]["net_duration"]
                )
            },
            "volume": {
                "total_volume": calculate_change_percentage(
                    current_stats["volume"]["total_volume"],
                    comparison_stats["volume"]["total_volume"]
                )
            },
            "exercises": {
                "unique_count": calculate_change_percentage(
                    current_stats["exercises"]["unique_count"],
                    comparison_stats["exercises"]["unique_count"]
                )
            },
            "sets_and_reps": {
                "total_sets": calculate_change_percentage(
                    current_stats["sets_and_reps"]["total_sets"],
                    comparison_stats["sets_and_reps"]["total_sets"]
                ),
                "total_reps": calculate_change_percentage(
                    current_stats["sets_and_reps"]["total_reps"],
                    comparison_stats["sets_and_reps"]["total_reps"]
                )
            }
        }
        
        return comparison