"""
EnhancedLangGraphService - 统一工作流编排服务

提供5种工作模式的统一接口，支持多阶段处理管道、智能工作流选择、
实时性能监控和全面的错误处理机制。
"""

import asyncio
import time
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

from sqlalchemy.orm import Session
from app.services.llm_proxy_service import LLMProxyService
from app.services.conversation.intent_recognition import EnhancedIntentRecognizer
from app.services.conversation.parameter_extractor import ParameterExtractor
from app.services.conversation.context_manager import ConversationContextManager
from app.core.config import settings

logger = logging.getLogger(__name__)


class WorkflowMode(Enum):
    """工作流模式枚举"""
    STANDARD = "standard"
    ENHANCED = "enhanced"
    STREAMING = "streaming"
    PARAMETER_COLLECTION = "parameter_collection"
    TRAINING_PLAN = "training_plan"


@dataclass
class WorkflowRequest:
    """工作流请求"""
    message: str
    user_id: str
    session_id: str
    context: Dict[str, Any] = field(default_factory=dict)
    stream_response: bool = False
    quick_intent: Optional[str] = None
    workflow_mode: Optional[str] = None


@dataclass
class WorkflowResponse:
    """工作流响应"""
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    workflow_mode: str = "standard"
    error: bool = False
    error_type: Optional[str] = None


@dataclass
class ProcessingContext:
    """处理上下文"""
    request: WorkflowRequest
    cleaned_input: str = ""
    intent: str = ""
    confidence: float = 0.0
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    user_profile: Dict[str, Any] = field(default_factory=dict)
    extracted_params: Dict[str, Any] = field(default_factory=dict)
    response: Optional[WorkflowResponse] = None
    stage_metrics: Dict[str, float] = field(default_factory=dict)


class ProcessingStage:
    """处理阶段基类"""
    
    def __init__(self, name: str):
        self.name = name
    
    async def process(self, context: ProcessingContext) -> ProcessingContext:
        """处理上下文（子类实现）"""
        raise NotImplementedError


class PreprocessingStage(ProcessingStage):
    """预处理阶段"""
    
    def __init__(self):
        super().__init__("preprocessing")
    
    async def process(self, context: ProcessingContext) -> ProcessingContext:
        """预处理阶段处理"""
        # 输入验证
        await self._validate_input(context.request)
        
        # 文本清理和标准化
        context.cleaned_input = await self._clean_text(context.request.message)
        
        # 上下文加载
        context.conversation_history = await self._load_context(
            context.request.session_id
        )
        
        return context
    
    async def _validate_input(self, request: WorkflowRequest):
        """验证输入"""
        if not request.message or len(request.message.strip()) == 0:
            raise ValueError("消息内容不能为空")
        
        if len(request.message) > 2000:
            raise ValueError("消息长度不能超过2000字符")
    
    async def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余空白字符
        cleaned = " ".join(text.split())
        
        # 移除特殊字符（保留基本标点）
        # 这里可以添加更复杂的文本清理逻辑
        
        return cleaned.strip()
    
    async def _load_context(self, session_id: str) -> List[Dict[str, Any]]:
        """加载对话上下文"""
        # 这里应该从数据库或缓存中加载对话历史
        # 暂时返回空列表
        return []


class IntentRecognitionStage(ProcessingStage):
    """意图识别阶段"""
    
    def __init__(self, intent_recognizer: EnhancedIntentRecognizer):
        super().__init__("intent_recognition")
        self.intent_recognizer = intent_recognizer
    
    async def process(self, context: ProcessingContext) -> ProcessingContext:
        """意图识别处理"""
        # 快速意图检测
        if context.request.quick_intent:
            context.intent = context.request.quick_intent
            context.confidence = 1.0
        else:
            # 智能意图识别
            intent_result = await self.intent_recognizer.recognize_intent(
                message=context.cleaned_input,
                context=context.conversation_history,
                user_profile=context.user_profile
            )
            context.intent = intent_result.get("intent", "general_chat")
            context.confidence = intent_result.get("confidence", 0.5)
        
        # 意图验证和修正
        if context.confidence < 0.7:
            context.intent = await self._clarify_intent(context)
        
        return context
    
    async def _clarify_intent(self, context: ProcessingContext) -> str:
        """澄清意图"""
        # 这里可以实现意图澄清逻辑
        # 暂时返回通用聊天意图
        return "general_chat"


class ParameterExtractionStage(ProcessingStage):
    """参数提取阶段"""
    
    def __init__(self, parameter_extractor: ParameterExtractor):
        super().__init__("parameter_extraction")
        self.parameter_extractor = parameter_extractor
    
    async def process(self, context: ProcessingContext) -> ProcessingContext:
        """参数提取处理"""
        # 基于意图提取相关参数
        if context.intent in ["training_plan_generation", "exercise_recommendation"]:
            context.extracted_params = await self.parameter_extractor.extract_parameters(
                message=context.cleaned_input,
                intent=context.intent,
                context=context.conversation_history
            )
        
        return context


class ExecutionStage(ProcessingStage):
    """执行阶段"""
    
    def __init__(self, enhanced_service):
        super().__init__("execution")
        self.enhanced_service = enhanced_service
    
    async def process(self, context: ProcessingContext) -> ProcessingContext:
        """执行阶段处理"""
        # 根据工作流模式执行相应逻辑
        workflow_mode = context.request.workflow_mode or await self._select_workflow_mode(context)
        
        workflow = self.enhanced_service.workflow_modes.get(workflow_mode)
        if workflow:
            response_content = await workflow.execute(context)
        else:
            response_content = "抱歉，无法处理您的请求。"
        
        # 构建响应
        context.response = WorkflowResponse(
            content=response_content,
            workflow_mode=workflow_mode,
            metadata={
                "intent": context.intent,
                "confidence": context.confidence,
                "extracted_params": context.extracted_params
            }
        )
        
        return context
    
    async def _select_workflow_mode(self, context: ProcessingContext) -> str:
        """选择工作流模式"""
        # 基于意图和上下文选择最适合的工作流
        if context.request.stream_response:
            return WorkflowMode.STREAMING.value
        
        if context.intent == "training_plan_generation":
            return WorkflowMode.TRAINING_PLAN.value
        
        if context.intent in ["exercise_recommendation", "nutrition_advice"]:
            return WorkflowMode.ENHANCED.value
        
        return WorkflowMode.STANDARD.value


class PostprocessingStage(ProcessingStage):
    """后处理阶段"""
    
    def __init__(self):
        super().__init__("postprocessing")
    
    async def process(self, context: ProcessingContext) -> ProcessingContext:
        """后处理阶段处理"""
        if context.response:
            # 添加处理时间
            total_time = sum(context.stage_metrics.values())
            context.response.processing_time = total_time
            
            # 添加调试信息（开发环境）
            if settings.DEBUG:
                context.response.metadata["debug"] = {
                    "stage_metrics": context.stage_metrics,
                    "processing_context": {
                        "intent": context.intent,
                        "confidence": context.confidence
                    }
                }
        
        return context


class ProcessingPipeline:
    """多阶段处理管道"""
    
    def __init__(self, stages: List[ProcessingStage]):
        self.stages = stages
        self.metrics = PipelineMetrics()
    
    async def process(self, request: WorkflowRequest) -> WorkflowResponse:
        """执行完整的处理管道"""
        context = ProcessingContext(request=request)
        
        for stage in self.stages:
            start_time = time.time()
            try:
                context = await stage.process(context)
                stage_time = time.time() - start_time
                context.stage_metrics[stage.name] = stage_time
                self.metrics.record_stage_time(stage.name, stage_time)
            except Exception as e:
                await self._handle_stage_error(stage, context, e)
                break
        
        return context.response or WorkflowResponse(
            content="处理请求时发生错误",
            error=True,
            error_type="pipeline_error"
        )
    
    async def _handle_stage_error(self, stage: ProcessingStage, 
                                 context: ProcessingContext, error: Exception):
        """处理阶段错误"""
        logger.error(f"处理阶段 {stage.name} 发生错误: {str(error)}")
        
        # 构建错误响应
        context.response = WorkflowResponse(
            content=f"在{stage.name}阶段处理时发生错误",
            error=True,
            error_type=f"{stage.name}_error",
            metadata={"error_detail": str(error)}
        )


class PipelineMetrics:
    """管道性能指标"""
    
    def __init__(self):
        self.stage_times: Dict[str, List[float]] = defaultdict(list)
        self.total_requests = 0
        self.error_count = 0
    
    def record_stage_time(self, stage_name: str, time_ms: float):
        """记录阶段执行时间"""
        self.stage_times[stage_name].append(time_ms * 1000)  # 转换为毫秒
    
    def get_average_stage_time(self, stage_name: str) -> float:
        """获取阶段平均执行时间"""
        times = self.stage_times.get(stage_name, [])
        return sum(times) / len(times) if times else 0.0
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {}
        for stage_name, times in self.stage_times.items():
            if times:
                summary[stage_name] = {
                    "avg_time_ms": sum(times) / len(times),
                    "max_time_ms": max(times),
                    "min_time_ms": min(times),
                    "total_executions": len(times)
                }
        return summary


class WorkflowBase:
    """工作流基类"""
    
    def __init__(self, enhanced_service):
        self.enhanced_service = enhanced_service
        self.llm_proxy = enhanced_service.llm_proxy
    
    async def execute(self, context: ProcessingContext) -> str:
        """执行工作流（子类实现）"""
        raise NotImplementedError


class StandardWorkflow(WorkflowBase):
    """标准工作流"""
    
    async def execute(self, context: ProcessingContext) -> str:
        """执行标准工作流"""
        # 简单的LLM调用
        response = await self.llm_proxy.aget_chat_response([
            {"role": "system", "content": "你是一个专业的健身教练。"},
            {"role": "user", "content": context.cleaned_input}
        ])
        
        return response


class EnhancedWorkflow(WorkflowBase):
    """增强工作流"""
    
    async def execute(self, context: ProcessingContext) -> str:
        """执行增强工作流"""
        # 构建更详细的上下文
        system_prompt = self._build_enhanced_system_prompt(context)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": context.cleaned_input}
        ]
        
        # 添加对话历史
        for msg in context.conversation_history[-5:]:  # 最近5条消息
            messages.append(msg)
        
        response = await self.llm_proxy.aget_chat_response(
            messages, model="agent-app"
        )
        
        return response
    
    def _build_enhanced_system_prompt(self, context: ProcessingContext) -> str:
        """构建增强系统提示"""
        base_prompt = "你是一个专业的健身教练和营养师。"
        
        # 添加用户画像信息
        if context.user_profile:
            user_info = []
            if context.user_profile.get("age"):
                user_info.append(f"年龄{context.user_profile['age']}岁")
            if context.user_profile.get("fitness_goal"):
                user_info.append(f"健身目标是{context.user_profile['fitness_goal']}")
            
            if user_info:
                base_prompt += f" 用户{', '.join(user_info)}。"
        
        # 添加意图相关指导
        if context.intent == "exercise_recommendation":
            base_prompt += " 请重点推荐适合的训练动作。"
        elif context.intent == "nutrition_advice":
            base_prompt += " 请重点提供营养建议。"
        
        return base_prompt


class StreamingWorkflow(WorkflowBase):
    """流式工作流"""
    
    async def execute(self, context: ProcessingContext) -> str:
        """执行流式工作流"""
        # 流式工作流的执行逻辑
        # 这里返回一个标识，实际的流式处理在其他地方实现
        return "[STREAMING_RESPONSE]"


class ParameterCollectionWorkflow(WorkflowBase):
    """参数收集工作流"""
    
    async def execute(self, context: ProcessingContext) -> str:
        """执行参数收集工作流"""
        # 检查缺失的参数
        missing_params = await self._check_missing_parameters(context)
        
        if missing_params:
            # 生成收集参数的问题
            question = await self._generate_parameter_question(missing_params[0], context)
            return question
        else:
            # 参数收集完成，转到其他工作流
            return "参数收集完成，正在为您生成个性化建议..."
    
    async def _check_missing_parameters(self, context: ProcessingContext) -> List[str]:
        """检查缺失的参数"""
        required_params = ["age", "gender", "fitness_goal", "experience_level"]
        missing = []
        
        for param in required_params:
            if param not in context.extracted_params and param not in context.user_profile:
                missing.append(param)
        
        return missing
    
    async def _generate_parameter_question(self, param: str, context: ProcessingContext) -> str:
        """生成参数收集问题"""
        questions = {
            "age": "请问您的年龄是多少？这有助于我为您制定更合适的健身计划。",
            "gender": "请问您的性别是？（男/女）这有助于我提供更精准的建议。",
            "fitness_goal": "请问您的健身目标是什么？（如：减脂、增肌、塑形、提高体能等）",
            "experience_level": "请问您的健身经验如何？（新手/初级/中级/高级）"
        }
        
        return questions.get(param, f"请提供您的{param}信息。")


class TrainingPlanWorkflow(WorkflowBase):
    """训练计划工作流"""
    
    async def execute(self, context: ProcessingContext) -> str:
        """执行训练计划工作流"""
        # 检查是否有足够的参数生成训练计划
        required_params = ["fitness_goal", "experience_level"]
        missing_params = [
            param for param in required_params
            if param not in context.extracted_params and param not in context.user_profile
        ]
        
        if missing_params:
            return f"为了生成个性化训练计划，我还需要了解您的{', '.join(missing_params)}。"
        
        # 生成训练计划
        plan_prompt = self._build_training_plan_prompt(context)
        
        response = await self.llm_proxy.aget_chat_response([
            {"role": "system", "content": "你是专业的健身教练，擅长制定个性化训练计划。"},
            {"role": "user", "content": plan_prompt}
        ], model="exercise-generation-app")
        
        return response
    
    def _build_training_plan_prompt(self, context: ProcessingContext) -> str:
        """构建训练计划生成提示"""
        user_info = {**context.user_profile, **context.extracted_params}
        
        prompt = f"请为以下用户制定一个个性化的训练计划：\n"
        prompt += f"健身目标：{user_info.get('fitness_goal', '未指定')}\n"
        prompt += f"经验水平：{user_info.get('experience_level', '未指定')}\n"
        
        if user_info.get("age"):
            prompt += f"年龄：{user_info['age']}岁\n"
        if user_info.get("training_frequency"):
            prompt += f"训练频率：{user_info['training_frequency']}\n"
        
        prompt += "\n请提供详细的训练计划，包括具体的动作、组数、次数和注意事项。"
        
        return prompt


class WorkflowPerformanceMonitor:
    """工作流性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.thresholds = {
            "standard": 100,      # ms
            "enhanced": 500,      # ms
            "streaming": 1000,    # ms
            "parameter_collection": 200,  # ms
            "training_plan": 1000  # ms
        }
        self.alert_history = deque(maxlen=100)
    
    async def monitor_workflow(self, workflow_name: str, execution_time: float):
        """监控工作流执行"""
        self.metrics[workflow_name].append(execution_time)
        
        # 检查性能阈值
        threshold = self.thresholds.get(workflow_name, 500)
        if execution_time > threshold:
            await self._handle_performance_issue(workflow_name, execution_time)
    
    async def _handle_performance_issue(self, workflow_name: str, execution_time: float):
        """处理性能问题"""
        alert = {
            "timestamp": datetime.now(),
            "workflow": workflow_name,
            "execution_time": execution_time,
            "threshold": self.thresholds.get(workflow_name, 500),
            "severity": "high" if execution_time > self.thresholds.get(workflow_name, 500) * 2 else "medium"
        }
        
        self.alert_history.append(alert)
        logger.warning(f"工作流性能告警: {workflow_name} 执行时间 {execution_time:.2f}ms 超过阈值")
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        report = {}
        for workflow_name, times in self.metrics.items():
            if times:
                report[workflow_name] = {
                    "avg_time": sum(times) / len(times),
                    "max_time": max(times),
                    "min_time": min(times),
                    "total_executions": len(times),
                    "threshold": self.thresholds.get(workflow_name, 500)
                }
        return report


class WorkflowErrorHandler:
    """工作流错误处理器"""
    
    def __init__(self):
        self.error_strategies = {
            "timeout": self._handle_timeout_error,
            "llm_error": self._handle_llm_error,
            "parameter_error": self._handle_parameter_error,
            "system_error": self._handle_system_error
        }
    
    async def handle_error(self, error: Exception, context: ProcessingContext) -> WorkflowResponse:
        """处理工作流错误"""
        error_type = self._classify_error(error)
        handler = self.error_strategies.get(error_type, self.error_strategies["system_error"])
        
        return await handler(error, context)
    
    def _classify_error(self, error: Exception) -> str:
        """分类错误类型"""
        if isinstance(error, asyncio.TimeoutError):
            return "timeout"
        elif "llm" in str(error).lower() or "api" in str(error).lower():
            return "llm_error"
        elif "parameter" in str(error).lower() or "validation" in str(error).lower():
            return "parameter_error"
        else:
            return "system_error"
    
    async def _handle_timeout_error(self, error: Exception, context: ProcessingContext) -> WorkflowResponse:
        """处理超时错误"""
        return WorkflowResponse(
            content="请求处理超时，请稍后重试。",
            error=True,
            error_type="timeout"
        )
    
    async def _handle_llm_error(self, error: Exception, context: ProcessingContext) -> WorkflowResponse:
        """处理LLM错误"""
        return WorkflowResponse(
            content="AI服务暂时不可用，请稍后重试。",
            error=True,
            error_type="llm_unavailable"
        )
    
    async def _handle_parameter_error(self, error: Exception, context: ProcessingContext) -> WorkflowResponse:
        """处理参数错误"""
        return WorkflowResponse(
            content="请求参数有误，请检查输入内容。",
            error=True,
            error_type="parameter_error"
        )
    
    async def _handle_system_error(self, error: Exception, context: ProcessingContext) -> WorkflowResponse:
        """处理系统错误"""
        return WorkflowResponse(
            content="系统暂时出现问题，请稍后重试。",
            error=True,
            error_type="system_error"
        )


class EnhancedLangGraphService:
    """增强LangGraph服务 - 统一工作流编排服务"""
    
    def __init__(self, db: Session, config: Dict[str, Any] = None):
        self.db = db
        self.config = config or {}
        
        # 核心服务
        self.llm_proxy = LLMProxyService()
        self.intent_recognizer = EnhancedIntentRecognizer(self.llm_proxy)
        self.parameter_extractor = ParameterExtractor(self.llm_proxy)
        self.context_manager = ConversationContextManager()
        
        # 工作流模式
        self.workflow_modes = {
            WorkflowMode.STANDARD.value: StandardWorkflow(self),
            WorkflowMode.ENHANCED.value: EnhancedWorkflow(self),
            WorkflowMode.STREAMING.value: StreamingWorkflow(self),
            WorkflowMode.PARAMETER_COLLECTION.value: ParameterCollectionWorkflow(self),
            WorkflowMode.TRAINING_PLAN.value: TrainingPlanWorkflow(self)
        }
        
        # 监控和优化
        self.performance_monitor = WorkflowPerformanceMonitor()
        self.error_handler = WorkflowErrorHandler()
        
        # 处理管道
        self.pipeline = ProcessingPipeline([
            PreprocessingStage(),
            IntentRecognitionStage(self.intent_recognizer),
            ParameterExtractionStage(self.parameter_extractor),
            ExecutionStage(self),
            PostprocessingStage()
        ])
        
        # 初始化状态
        self.initialized = False
    
    async def initialize(self):
        """初始化服务"""
        if self.initialized:
            return
        
        try:
            # 初始化各个组件
            await self.intent_recognizer.initialize()
            await self.context_manager.initialize()
            
            self.initialized = True
            logger.info("EnhancedLangGraphService 初始化完成")
            
        except Exception as e:
            logger.error(f"EnhancedLangGraphService 初始化失败: {str(e)}")
            raise
    
    async def process_request(self, request: WorkflowRequest) -> WorkflowResponse:
        """处理工作流请求"""
        if not self.initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # 执行处理管道
            response = await self.pipeline.process(request)
            
            # 记录性能指标
            execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
            await self.performance_monitor.monitor_workflow(
                response.workflow_mode, execution_time
            )
            
            return response
            
        except Exception as e:
            logger.error(f"处理工作流请求失败: {str(e)}")
            
            # 使用错误处理器处理错误
            context = ProcessingContext(request=request)
            return await self.error_handler.handle_error(e, context)
    
    async def process_request_stream(self, request: WorkflowRequest) -> AsyncGenerator[Dict[str, Any], None]:
        """流式处理工作流请求"""
        if not self.initialized:
            await self.initialize()
        
        # 设置流式响应标志
        request.stream_response = True
        
        try:
            # 先进行常规处理获取基本信息
            response = await self.process_request(request)
            
            if response.error:
                yield {"type": "error", "content": response.content, "error_type": response.error_type}
                return
            
            # 如果是流式工作流，进行流式处理
            if response.workflow_mode == WorkflowMode.STREAMING.value:
                async for chunk in self._stream_response(request, response):
                    yield chunk
            else:
                # 非流式工作流，直接返回完整响应
                yield {"type": "message", "content": response.content, "metadata": response.metadata}
        
        except Exception as e:
            logger.error(f"流式处理失败: {str(e)}")
            yield {"type": "error", "content": "处理请求时发生错误", "error": str(e)}
    
    async def _stream_response(self, request: WorkflowRequest, 
                              initial_response: WorkflowResponse) -> AsyncGenerator[Dict[str, Any], None]:
        """流式响应处理"""
        # 构建流式LLM请求
        messages = [
            {"role": "system", "content": "你是一个专业的健身教练。请提供详细的回答。"},
            {"role": "user", "content": request.message}
        ]
        
        # 流式调用LLM
        async for chunk in self.llm_proxy.stream_chat(messages, model="agent-app"):
            yield {"type": "token", "content": chunk}
        
        # 流式完成
        yield {"type": "stream_complete", "metadata": initial_response.metadata}
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            "workflow_performance": await self.performance_monitor.get_performance_report(),
            "pipeline_performance": self.pipeline.metrics.get_performance_summary(),
            "system_status": {
                "initialized": self.initialized,
                "active_workflows": len(self.workflow_modes),
                "uptime": self._get_uptime()
            }
        }
    
    def _get_uptime(self) -> str:
        """获取运行时间"""
        # 这里应该记录服务启动时间并计算运行时间
        return "运行中"
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "healthy": True,
            "components": {},
            "timestamp": datetime.now().isoformat()
        }
        
        # 检查各个组件状态
        try:
            # 检查LLM服务
            test_response = await self.llm_proxy.aget_chat_response([
                {"role": "user", "content": "test"}
            ])
            health_status["components"]["llm_service"] = {
                "status": "healthy" if test_response else "unhealthy"
            }
        except Exception as e:
            health_status["components"]["llm_service"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["healthy"] = False
        
        # 检查数据库连接
        try:
            # 这里应该执行数据库连接测试
            health_status["components"]["database"] = {"status": "healthy"}
        except Exception as e:
            health_status["components"]["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["healthy"] = False
        
        return health_status