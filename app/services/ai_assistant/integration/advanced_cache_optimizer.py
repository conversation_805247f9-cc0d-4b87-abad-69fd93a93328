"""
高级缓存优化器

基于现有的缓存管理器，实现高级缓存优化策略：
- 智能缓存预热 (Intelligent Cache Warming)
- 分层缓存架构 (Tiered Cache Architecture)
- 自适应缓存策略 (Adaptive Cache Strategy)
- 缓存压缩和序列化优化 (Cache Compression & Serialization)
- 分布式缓存一致性 (Distributed Cache Consistency)
"""

import asyncio
import pickle
import gzip
import hashlib
import time
from typing import Dict, Any, Optional, List, Tuple, Union, Callable
from datetime import datetime, timedelta
from collections import defaultdict, OrderedDict
from dataclasses import dataclass, field
from enum import Enum
import json

import redis.asyncio as aioredis
from sqlalchemy.ext.asyncio import AsyncSession

from .cache_manager import CacheManager  # 使用现有的缓存管理器
from ...logger.logger import get_logger

logger = get_logger(__name__)


class CacheLevel(Enum):
    """缓存层级"""
    L1_MEMORY = "l1_memory"      # L1: 内存缓存
    L2_REDIS = "l2_redis"        # L2: Redis缓存
    L3_DATABASE = "l3_database"  # L3: 数据库缓存


class CacheStrategy(Enum):
    """缓存策略"""
    LRU = "lru"                  # 最近最少使用
    LFU = "lfu"                  # 最少使用频率
    TTL = "ttl"                  # 基于时间
    ADAPTIVE = "adaptive"        # 自适应
    PREDICTIVE = "predictive"    # 预测性


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    size_bytes: int = 0
    ttl_seconds: Optional[int] = None
    compression_ratio: float = 1.0
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl_seconds is None:
            return False
        return (datetime.now() - self.created_at).total_seconds() > self.ttl_seconds
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = datetime.now()
        self.access_count += 1


@dataclass
class CacheStats:
    """缓存统计"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    compressions: int = 0
    total_size: int = 0
    entries_count: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class IntelligentCacheWarmer:
    """智能缓存预热器"""
    
    def __init__(self, cache_optimizer):
        self.cache_optimizer = cache_optimizer
        self.warming_patterns: Dict[str, List] = defaultdict(list)
        self.access_patterns: Dict[str, List] = defaultdict(list)
        self.warming_tasks: Dict[str, asyncio.Task] = {}
        
    async def analyze_access_patterns(self, key: str, access_time: datetime):
        """分析访问模式"""
        self.access_patterns[key].append(access_time)
        
        # 保持最近100次访问记录
        if len(self.access_patterns[key]) > 100:
            self.access_patterns[key] = self.access_patterns[key][-100:]
        
        # 分析是否需要预热
        if len(self.access_patterns[key]) >= 10:
            await self._detect_warming_opportunities(key)
    
    async def _detect_warming_opportunities(self, key: str):
        """检测预热机会"""
        accesses = self.access_patterns[key]
        
        # 计算访问间隔
        intervals = []
        for i in range(1, len(accesses)):
            interval = (accesses[i] - accesses[i-1]).total_seconds()
            intervals.append(interval)
        
        if not intervals:
            return
        
        # 检测规律性访问模式
        avg_interval = sum(intervals) / len(intervals)
        variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
        
        # 如果访问模式规律且频繁（方差小，平均间隔短）
        if variance < (avg_interval * 0.5) ** 2 and avg_interval < 3600:  # 1小时内
            await self._schedule_prewarming(key, avg_interval)
    
    async def _schedule_prewarming(self, key: str, interval: float):
        """调度预热任务"""
        if key in self.warming_tasks:
            self.warming_tasks[key].cancel()
        
        async def warming_task():
            while True:
                try:
                    await asyncio.sleep(interval * 0.8)  # 提前20%预热
                    
                    # 检查缓存是否即将过期
                    entry = await self.cache_optimizer.get_entry_info(key)
                    if entry and entry.ttl_seconds:
                        remaining_ttl = entry.ttl_seconds - (datetime.now() - entry.created_at).total_seconds()
                        if remaining_ttl < interval * 0.5:
                            # 预热缓存
                            await self.cache_optimizer.prewarm_cache(key)
                            logger.info(f"预热缓存键: {key}")
                
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"缓存预热错误: {key}, 错误: {str(e)}")
        
        self.warming_tasks[key] = asyncio.create_task(warming_task())


class CompressionManager:
    """压缩管理器"""
    
    def __init__(self):
        self.compression_threshold = 1024  # 1KB以上启用压缩
        self.compression_stats = defaultdict(int)
    
    def should_compress(self, data: bytes) -> bool:
        """判断是否应该压缩"""
        return len(data) >= self.compression_threshold
    
    def compress(self, data: Any) -> Tuple[bytes, float]:
        """压缩数据"""
        try:
            # 序列化
            serialized = pickle.dumps(data)
            original_size = len(serialized)
            
            if not self.should_compress(serialized):
                return serialized, 1.0
            
            # 压缩
            compressed = gzip.compress(serialized)
            compressed_size = len(compressed)
            compression_ratio = compressed_size / original_size
            
            self.compression_stats["compressions"] += 1
            self.compression_stats["original_bytes"] += original_size
            self.compression_stats["compressed_bytes"] += compressed_size
            
            return compressed, compression_ratio
            
        except Exception as e:
            logger.error(f"压缩失败: {str(e)}")
            return pickle.dumps(data), 1.0
    
    def decompress(self, data: bytes, compression_ratio: float) -> Any:
        """解压数据"""
        try:
            if compression_ratio >= 1.0:
                # 未压缩数据
                return pickle.loads(data)
            else:
                # 压缩数据
                decompressed = gzip.decompress(data)
                return pickle.loads(decompressed)
                
        except Exception as e:
            logger.error(f"解压失败: {str(e)}")
            raise
    
    def get_compression_stats(self) -> Dict[str, Any]:
        """获取压缩统计"""
        stats = dict(self.compression_stats)
        if stats.get("original_bytes", 0) > 0:
            stats["overall_compression_ratio"] = stats.get("compressed_bytes", 0) / stats["original_bytes"]
            stats["space_saved_bytes"] = stats["original_bytes"] - stats.get("compressed_bytes", 0)
        return stats


class AdvancedCacheOptimizer:
    """高级缓存优化器"""
    
    def __init__(self, db_session: AsyncSession, redis_url: str = None):
        self.db_session = db_session
        
        # 基础缓存管理器
        self.base_cache_manager = CacheManager()
        
        # 高级组件
        self.cache_warmer = IntelligentCacheWarmer(self)
        self.compression_manager = CompressionManager()
        
        # 多层缓存
        self.l1_cache: OrderedDict[str, CacheEntry] = OrderedDict()  # 内存缓存
        self.l2_redis_client: Optional[aioredis.Redis] = None        # Redis缓存
        
        # 配置
        self.l1_max_size = 1000  # L1缓存最大条目数
        self.l1_max_bytes = 100 * 1024 * 1024  # L1缓存最大字节数 (100MB)
        self.default_ttl = 3600  # 默认TTL (1小时)
        
        # 统计
        self.stats_by_level = {
            CacheLevel.L1_MEMORY: CacheStats(),
            CacheLevel.L2_REDIS: CacheStats(),
            CacheLevel.L3_DATABASE: CacheStats(),
        }
        
        # 自适应策略
        self.adaptive_strategy = CacheStrategy.ADAPTIVE
        self.strategy_weights = {
            CacheStrategy.LRU: 0.4,
            CacheStrategy.LFU: 0.3,
            CacheStrategy.TTL: 0.3
        }
        
        # 初始化Redis连接
        if redis_url:
            asyncio.create_task(self._init_redis(redis_url))
        
        logger.info("高级缓存优化器初始化完成")
    
    async def _init_redis(self, redis_url: str):
        """初始化Redis连接"""
        try:
            self.l2_redis_client = aioredis.from_url(redis_url)
            await self.l2_redis_client.ping()
            logger.info("Redis连接建立成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            self.l2_redis_client = None
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值 - 多层查找"""
        start_time = time.time()
        
        try:
            # L1缓存查找
            result = await self._get_from_l1(key)
            if result is not None:
                await self.cache_warmer.analyze_access_patterns(key, datetime.now())
                return result
            
            # L2缓存查找
            if self.l2_redis_client:
                result = await self._get_from_l2(key)
                if result is not None:
                    # 提升到L1
                    await self._promote_to_l1(key, result)
                    await self.cache_warmer.analyze_access_patterns(key, datetime.now())
                    return result
            
            # L3数据库查找
            result = await self._get_from_l3(key)
            if result is not None:
                # 提升到上层缓存
                if self.l2_redis_client:
                    await self._set_to_l2(key, result)
                await self._promote_to_l1(key, result)
                await self.cache_warmer.analyze_access_patterns(key, datetime.now())
                return result
            
            # 缓存未命中
            self._record_miss(CacheLevel.L1_MEMORY)
            return default
            
        except Exception as e:
            logger.error(f"缓存获取错误: {key}, 错误: {str(e)}")
            return default
        finally:
            latency = (time.time() - start_time) * 1000
            logger.debug(f"缓存查找延迟: {latency:.2f}ms, 键: {key}")
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        level: CacheLevel = CacheLevel.L1_MEMORY
    ) -> bool:
        """设置缓存值"""
        try:
            ttl = ttl or self.default_ttl
            
            # 根据级别设置缓存
            if level == CacheLevel.L1_MEMORY:
                await self._set_to_l1(key, value, ttl)
            elif level == CacheLevel.L2_REDIS and self.l2_redis_client:
                await self._set_to_l2(key, value, ttl)
            elif level == CacheLevel.L3_DATABASE:
                await self._set_to_l3(key, value, ttl)
            
            # 同时设置到下层缓存
            if level == CacheLevel.L1_MEMORY and self.l2_redis_client:
                await self._set_to_l2(key, value, ttl)
            
            return True
            
        except Exception as e:
            logger.error(f"缓存设置错误: {key}, 错误: {str(e)}")
            return False
    
    async def _get_from_l1(self, key: str) -> Any:
        """从L1缓存获取"""
        entry = self.l1_cache.get(key)
        if entry is None:
            self._record_miss(CacheLevel.L1_MEMORY)
            return None
        
        if entry.is_expired():
            await self._evict_from_l1(key)
            self._record_miss(CacheLevel.L1_MEMORY)
            return None
        
        # 更新访问信息
        entry.update_access()
        
        # LRU更新
        self.l1_cache.move_to_end(key)
        
        self._record_hit(CacheLevel.L1_MEMORY)
        return entry.value
    
    async def _get_from_l2(self, key: str) -> Any:
        """从L2缓存获取"""
        if not self.l2_redis_client:
            return None
        
        try:
            # 获取压缩数据和元数据
            pipe = self.l2_redis_client.pipeline()
            pipe.get(f"data:{key}")
            pipe.hgetall(f"meta:{key}")
            results = await pipe.execute()
            
            data, meta = results
            if data is None or not meta:
                self._record_miss(CacheLevel.L2_REDIS)
                return None
            
            # 解压和反序列化
            compression_ratio = float(meta.get("compression_ratio", 1.0))
            value = self.compression_manager.decompress(data, compression_ratio)
            
            self._record_hit(CacheLevel.L2_REDIS)
            return value
            
        except Exception as e:
            logger.error(f"L2缓存获取错误: {key}, 错误: {str(e)}")
            self._record_miss(CacheLevel.L2_REDIS)
            return None
    
    async def _get_from_l3(self, key: str) -> Any:
        """从L3数据库缓存获取"""
        try:
            # 使用基础缓存管理器的数据库缓存功能
            result = await self.base_cache_manager.get_from_database(key)
            
            if result is not None:
                self._record_hit(CacheLevel.L3_DATABASE)
                return result
            else:
                self._record_miss(CacheLevel.L3_DATABASE)
                return None
                
        except Exception as e:
            logger.error(f"L3缓存获取错误: {key}, 错误: {str(e)}")
            self._record_miss(CacheLevel.L3_DATABASE)
            return None
    
    async def _set_to_l1(self, key: str, value: Any, ttl: int):
        """设置到L1缓存"""
        # 计算大小
        compressed_data, compression_ratio = self.compression_manager.compress(value)
        size_bytes = len(compressed_data)
        
        # 创建缓存条目
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            size_bytes=size_bytes,
            ttl_seconds=ttl,
            compression_ratio=compression_ratio
        )
        
        # 检查容量限制
        await self._ensure_l1_capacity(size_bytes)
        
        # 添加到缓存
        self.l1_cache[key] = entry
        self.stats_by_level[CacheLevel.L1_MEMORY].entries_count += 1
        self.stats_by_level[CacheLevel.L1_MEMORY].total_size += size_bytes
    
    async def _set_to_l2(self, key: str, value: Any, ttl: int):
        """设置到L2缓存"""
        if not self.l2_redis_client:
            return
        
        try:
            # 压缩数据
            compressed_data, compression_ratio = self.compression_manager.compress(value)
            
            # 设置数据和元数据
            pipe = self.l2_redis_client.pipeline()
            pipe.setex(f"data:{key}", ttl, compressed_data)
            pipe.hset(f"meta:{key}", mapping={
                "compression_ratio": compression_ratio,
                "created_at": datetime.now().isoformat(),
                "size_bytes": len(compressed_data)
            })
            pipe.expire(f"meta:{key}", ttl)
            await pipe.execute()
            
            self.stats_by_level[CacheLevel.L2_REDIS].entries_count += 1
            self.stats_by_level[CacheLevel.L2_REDIS].total_size += len(compressed_data)
            
        except Exception as e:
            logger.error(f"L2缓存设置错误: {key}, 错误: {str(e)}")
    
    async def _set_to_l3(self, key: str, value: Any, ttl: int):
        """设置到L3数据库缓存"""
        try:
            await self.base_cache_manager.set_to_database(key, value, ttl)
            self.stats_by_level[CacheLevel.L3_DATABASE].entries_count += 1
        except Exception as e:
            logger.error(f"L3缓存设置错误: {key}, 错误: {str(e)}")
    
    async def _promote_to_l1(self, key: str, value: Any):
        """提升到L1缓存"""
        await self._set_to_l1(key, value, self.default_ttl)
    
    async def _ensure_l1_capacity(self, new_size: int):
        """确保L1缓存容量"""
        current_size = sum(entry.size_bytes for entry in self.l1_cache.values())
        
        # 检查条目数限制
        while len(self.l1_cache) >= self.l1_max_size:
            await self._evict_l1_entry()
        
        # 检查大小限制
        while current_size + new_size > self.l1_max_bytes and self.l1_cache:
            evicted_size = await self._evict_l1_entry()
            current_size -= evicted_size
    
    async def _evict_l1_entry(self) -> int:
        """淘汰L1缓存条目"""
        if not self.l1_cache:
            return 0
        
        # 选择淘汰策略
        if self.adaptive_strategy == CacheStrategy.ADAPTIVE:
            key_to_evict = await self._select_adaptive_eviction()
        elif self.adaptive_strategy == CacheStrategy.LRU:
            key_to_evict = next(iter(self.l1_cache))  # 最少最近使用
        elif self.adaptive_strategy == CacheStrategy.LFU:
            key_to_evict = min(self.l1_cache.keys(), 
                             key=lambda k: self.l1_cache[k].access_count)
        else:
            key_to_evict = next(iter(self.l1_cache))
        
        return await self._evict_from_l1(key_to_evict)
    
    async def _evict_from_l1(self, key: str) -> int:
        """从L1缓存淘汰指定条目"""
        entry = self.l1_cache.pop(key, None)
        if entry:
            self.stats_by_level[CacheLevel.L1_MEMORY].evictions += 1
            self.stats_by_level[CacheLevel.L1_MEMORY].entries_count -= 1
            self.stats_by_level[CacheLevel.L1_MEMORY].total_size -= entry.size_bytes
            return entry.size_bytes
        return 0
    
    async def _select_adaptive_eviction(self) -> str:
        """自适应淘汰选择"""
        if not self.l1_cache:
            return ""
        
        # 综合多种策略的评分
        scores = {}
        for key, entry in self.l1_cache.items():
            lru_score = len(self.l1_cache) - list(self.l1_cache.keys()).index(key)
            lfu_score = entry.access_count
            ttl_score = (datetime.now() - entry.created_at).total_seconds()
            
            # 标准化评分
            lru_norm = lru_score / len(self.l1_cache)
            lfu_norm = lfu_score / max(e.access_count for e in self.l1_cache.values())
            ttl_norm = ttl_score / max((datetime.now() - e.created_at).total_seconds() 
                                     for e in self.l1_cache.values())
            
            # 加权综合评分（分数越低越应该被淘汰）
            scores[key] = (
                self.strategy_weights[CacheStrategy.LRU] * lru_norm +
                self.strategy_weights[CacheStrategy.LFU] * lfu_norm +
                self.strategy_weights[CacheStrategy.TTL] * (1 - ttl_norm)  # TTL越大越应该淘汰
            )
        
        return min(scores.keys(), key=scores.get)
    
    def _record_hit(self, level: CacheLevel):
        """记录缓存命中"""
        self.stats_by_level[level].hits += 1
    
    def _record_miss(self, level: CacheLevel):
        """记录缓存未命中"""
        self.stats_by_level[level].misses += 1
    
    async def prewarm_cache(self, key: str):
        """预热缓存"""
        try:
            # 触发数据重新加载
            await self._get_from_l3(key)
            logger.info(f"缓存预热完成: {key}")
        except Exception as e:
            logger.error(f"缓存预热失败: {key}, 错误: {str(e)}")
    
    async def get_entry_info(self, key: str) -> Optional[CacheEntry]:
        """获取缓存条目信息"""
        return self.l1_cache.get(key)
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计"""
        stats = {}
        
        # 各层缓存统计
        for level, cache_stats in self.stats_by_level.items():
            stats[level.value] = {
                "hits": cache_stats.hits,
                "misses": cache_stats.misses,
                "hit_rate": cache_stats.hit_rate,
                "evictions": cache_stats.evictions,
                "entries_count": cache_stats.entries_count,
                "total_size": cache_stats.total_size
            }
        
        # 压缩统计
        stats["compression"] = self.compression_manager.get_compression_stats()
        
        # L1缓存详细信息
        stats["l1_details"] = {
            "current_entries": len(self.l1_cache),
            "max_entries": self.l1_max_size,
            "current_size_mb": sum(e.size_bytes for e in self.l1_cache.values()) / (1024 * 1024),
            "max_size_mb": self.l1_max_bytes / (1024 * 1024),
            "average_entry_size": (sum(e.size_bytes for e in self.l1_cache.values()) / 
                                 len(self.l1_cache)) if self.l1_cache else 0
        }
        
        # 预热统计
        stats["warming"] = {
            "active_warming_tasks": len(self.cache_warmer.warming_tasks),
            "patterns_tracked": len(self.cache_warmer.access_patterns)
        }
        
        return stats
    
    async def optimize_cache_strategy(self):
        """优化缓存策略"""
        try:
            # 分析命中率
            total_hits = sum(stats.hits for stats in self.stats_by_level.values())
            total_requests = sum(stats.hits + stats.misses for stats in self.stats_by_level.values())
            
            if total_requests < 100:  # 样本太少
                return
            
            overall_hit_rate = total_hits / total_requests
            
            # 根据命中率调整策略权重
            if overall_hit_rate < 0.7:  # 命中率低，增加LFU权重
                self.strategy_weights[CacheStrategy.LFU] += 0.1
                self.strategy_weights[CacheStrategy.LRU] -= 0.05
                self.strategy_weights[CacheStrategy.TTL] -= 0.05
            elif overall_hit_rate > 0.9:  # 命中率高，增加LRU权重
                self.strategy_weights[CacheStrategy.LRU] += 0.1
                self.strategy_weights[CacheStrategy.LFU] -= 0.05
                self.strategy_weights[CacheStrategy.TTL] -= 0.05
            
            # 确保权重和为1
            total_weight = sum(self.strategy_weights.values())
            for strategy in self.strategy_weights:
                self.strategy_weights[strategy] /= total_weight
            
            logger.info(f"缓存策略权重已优化: {self.strategy_weights}")
            
        except Exception as e:
            logger.error(f"缓存策略优化失败: {str(e)}")
    
    async def cleanup_expired_entries(self):
        """清理过期条目"""
        expired_keys = []
        for key, entry in self.l1_cache.items():
            if entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            await self._evict_from_l1(key)
        
        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")
    
    async def shutdown(self):
        """关闭缓存优化器"""
        try:
            # 取消预热任务
            for task in self.cache_warmer.warming_tasks.values():
                task.cancel()
            
            # 关闭Redis连接
            if self.l2_redis_client:
                await self.l2_redis_client.close()
            
            logger.info("高级缓存优化器已关闭")
            
        except Exception as e:
            logger.error(f"缓存优化器关闭错误: {str(e)}")


# 定期优化任务
async def run_cache_optimization_task(optimizer: AdvancedCacheOptimizer):
    """运行缓存优化任务"""
    while True:
        try:
            await asyncio.sleep(300)  # 每5分钟优化一次
            
            await optimizer.optimize_cache_strategy()
            await optimizer.cleanup_expired_entries()
            
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error(f"缓存优化任务错误: {str(e)}") 