"""
WebSocket性能优化器

针对高并发场景的WebSocket优化：
- 背压控制 (Backpressure Control)
- 连接池管理 (Connection Pool Management)
- 消息队列和批处理 (Message Queue & Batching)
- 实时性能监控 (Real-time Performance Monitoring)
- 自适应流量控制 (Adaptive Flow Control)
"""

import asyncio
import time
import weakref
from typing import Dict, Any, Optional, List, Set, Callable, AsyncGenerator
from datetime import datetime, timedelta
from collections import deque, defaultdict
from dataclasses import dataclass, field
from enum import Enum
import json

from fastapi import WebSocket, WebSocketDisconnect
from starlette.websockets import WebSocketState

from ...logger.logger import get_logger

logger = get_logger(__name__)


class ConnectionStatus(Enum):
    """连接状态枚举"""
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTING = "disconnecting"
    DISCONNECTED = "disconnected"
    ERROR = "error"


@dataclass
class ConnectionMetrics:
    """连接指标"""
    connection_id: str
    connected_at: datetime
    last_activity: datetime
    messages_sent: int = 0
    messages_received: int = 0
    bytes_sent: int = 0
    bytes_received: int = 0
    errors: int = 0
    latency_samples: deque = field(default_factory=lambda: deque(maxlen=100))
    
    def update_activity(self):
        """更新活动时间"""
        self.last_activity = datetime.now()
    
    def add_sent_message(self, size: int):
        """记录发送消息"""
        self.messages_sent += 1
        self.bytes_sent += size
        self.update_activity()
    
    def add_received_message(self, size: int):
        """记录接收消息"""
        self.messages_received += 1
        self.bytes_received += size
        self.update_activity()
    
    def add_latency_sample(self, latency: float):
        """添加延迟样本"""
        self.latency_samples.append(latency)
    
    def get_average_latency(self) -> float:
        """获取平均延迟"""
        if not self.latency_samples:
            return 0.0
        return sum(self.latency_samples) / len(self.latency_samples)


@dataclass
class BackpressureConfig:
    """背压控制配置"""
    max_buffer_size: int = 1000  # 最大缓冲区大小
    high_watermark: int = 800    # 高水位标记
    low_watermark: int = 200     # 低水位标记
    drop_threshold: int = 950    # 丢弃阈值
    flow_control_window: int = 100  # 流控窗口大小


@dataclass
class QueuedMessage:
    """队列消息"""
    content: Dict[str, Any]
    priority: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    retries: int = 0
    max_retries: int = 3


class WebSocketConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self, connection_id: str, websocket: WebSocket):
        self.connection_id = connection_id
        self.websocket = websocket
        self.status = ConnectionStatus.CONNECTING
        self.metrics = ConnectionMetrics(connection_id, datetime.now(), datetime.now())
        
        # 消息队列
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.priority_queue: List[QueuedMessage] = []
        
        # 背压控制
        self.backpressure_active = False
        self.buffer_size = 0
        
        # 流控
        self.flow_control_active = False
        self.pending_acks = set()
        
        # 任务
        self.sender_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """启动连接管理"""
        try:
            await self.websocket.accept()
            self.status = ConnectionStatus.CONNECTED
            
            # 启动发送器任务
            self.sender_task = asyncio.create_task(self._message_sender())
            
            # 启动心跳任务
            self.heartbeat_task = asyncio.create_task(self._heartbeat_monitor())
            
            logger.info(f"WebSocket连接已建立: {self.connection_id}")
            
        except Exception as e:
            self.status = ConnectionStatus.ERROR
            logger.error(f"WebSocket连接失败: {self.connection_id}, 错误: {str(e)}")
            raise
    
    async def send_message(self, message: Dict[str, Any], priority: int = 0) -> bool:
        """发送消息（支持优先级）"""
        try:
            if self.status != ConnectionStatus.CONNECTED:
                return False
            
            queued_msg = QueuedMessage(content=message, priority=priority)
            
            # 检查背压
            if self.buffer_size >= BackpressureConfig.drop_threshold:
                logger.warning(f"消息被丢弃 - 缓冲区满: {self.connection_id}")
                return False
            
            # 优先级队列插入
            if priority > 0:
                self.priority_queue.append(queued_msg)
                self.priority_queue.sort(key=lambda x: x.priority, reverse=True)
            else:
                await self.message_queue.put(queued_msg)
            
            self.buffer_size += 1
            return True
            
        except Exception as e:
            logger.error(f"发送消息失败: {self.connection_id}, 错误: {str(e)}")
            return False
    
    async def _message_sender(self):
        """消息发送器任务"""
        while self.status == ConnectionStatus.CONNECTED:
            try:
                # 优先处理高优先级消息
                if self.priority_queue:
                    message = self.priority_queue.pop(0)
                else:
                    # 等待普通消息
                    message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                
                # 发送消息
                await self._send_single_message(message)
                self.buffer_size -= 1
                
                # 背压控制
                await self._handle_backpressure()
                
            except asyncio.TimeoutError:
                # 定期检查连接状态
                continue
            except Exception as e:
                logger.error(f"消息发送器错误: {self.connection_id}, 错误: {str(e)}")
                break
    
    async def _send_single_message(self, message: QueuedMessage):
        """发送单个消息"""
        try:
            start_time = time.time()
            
            # 序列化消息
            serialized = json.dumps(message.content)
            
            # 发送
            await self.websocket.send_text(serialized)
            
            # 记录指标
            latency = (time.time() - start_time) * 1000  # 毫秒
            self.metrics.add_sent_message(len(serialized))
            self.metrics.add_latency_sample(latency)
            
        except Exception as e:
            message.retries += 1
            if message.retries <= message.max_retries:
                # 重试
                if message.priority > 0:
                    self.priority_queue.insert(0, message)
                else:
                    await self.message_queue.put(message)
                self.buffer_size += 1
            else:
                logger.error(f"消息发送失败，超过重试次数: {self.connection_id}")
            raise
    
    async def _handle_backpressure(self):
        """处理背压"""
        config = BackpressureConfig()
        
        if self.buffer_size >= config.high_watermark and not self.backpressure_active:
            # 激活背压控制
            self.backpressure_active = True
            logger.warning(f"激活背压控制: {self.connection_id}, 缓冲区大小: {self.buffer_size}")
            
            # 发送背压信号给客户端
            await self._send_control_message({
                "type": "backpressure",
                "action": "slow_down",
                "buffer_size": self.buffer_size
            })
        
        elif self.buffer_size <= config.low_watermark and self.backpressure_active:
            # 停用背压控制
            self.backpressure_active = False
            logger.info(f"停用背压控制: {self.connection_id}, 缓冲区大小: {self.buffer_size}")
            
            # 发送恢复信号
            await self._send_control_message({
                "type": "backpressure",
                "action": "resume",
                "buffer_size": self.buffer_size
            })
    
    async def _send_control_message(self, control_msg: Dict[str, Any]):
        """发送控制消息"""
        try:
            control_msg.update({
                "timestamp": datetime.now().isoformat(),
                "connection_id": self.connection_id
            })
            
            serialized = json.dumps(control_msg)
            await self.websocket.send_text(serialized)
            
        except Exception as e:
            logger.error(f"发送控制消息失败: {self.connection_id}, 错误: {str(e)}")
    
    async def _heartbeat_monitor(self):
        """心跳监控"""
        heartbeat_interval = 30  # 30秒心跳
        
        while self.status == ConnectionStatus.CONNECTED:
            try:
                await asyncio.sleep(heartbeat_interval)
                
                # 发送心跳
                await self._send_control_message({
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat()
                })
                
                # 检查连接健康
                if datetime.now() - self.metrics.last_activity > timedelta(minutes=5):
                    logger.warning(f"连接可能不活跃: {self.connection_id}")
                
            except Exception as e:
                logger.error(f"心跳监控错误: {self.connection_id}, 错误: {str(e)}")
                break
    
    async def receive_message(self) -> Optional[Dict[str, Any]]:
        """接收消息"""
        try:
            if self.websocket.client_state != WebSocketState.CONNECTED:
                return None
            
            # 接收消息
            raw_message = await self.websocket.receive_text()
            
            # 解析消息
            message = json.loads(raw_message)
            
            # 记录指标
            self.metrics.add_received_message(len(raw_message))
            
            return message
            
        except WebSocketDisconnect:
            self.status = ConnectionStatus.DISCONNECTED
            logger.info(f"WebSocket连接断开: {self.connection_id}")
            return None
        except Exception as e:
            self.metrics.errors += 1
            logger.error(f"接收消息错误: {self.connection_id}, 错误: {str(e)}")
            return None
    
    async def close(self):
        """关闭连接"""
        self.status = ConnectionStatus.DISCONNECTING
        
        try:
            # 取消任务
            if self.sender_task:
                self.sender_task.cancel()
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
            
            # 关闭WebSocket
            if self.websocket.client_state == WebSocketState.CONNECTED:
                await self.websocket.close()
            
            self.status = ConnectionStatus.DISCONNECTED
            logger.info(f"WebSocket连接已关闭: {self.connection_id}")
            
        except Exception as e:
            logger.error(f"关闭连接错误: {self.connection_id}, 错误: {str(e)}")
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取状态信息"""
        return {
            "connection_id": self.connection_id,
            "status": self.status.value,
            "connected_at": self.metrics.connected_at.isoformat(),
            "last_activity": self.metrics.last_activity.isoformat(),
            "messages_sent": self.metrics.messages_sent,
            "messages_received": self.metrics.messages_received,
            "bytes_sent": self.metrics.bytes_sent,
            "bytes_received": self.metrics.bytes_received,
            "errors": self.metrics.errors,
            "average_latency": self.metrics.get_average_latency(),
            "buffer_size": self.buffer_size,
            "backpressure_active": self.backpressure_active
        }


class WebSocketOptimizer:
    """WebSocket性能优化器"""
    
    def __init__(self):
        # 连接池
        self.connections: Dict[str, WebSocketConnectionManager] = {}
        self.connection_groups: Dict[str, Set[str]] = defaultdict(set)  # 按组管理连接
        
        # 全局配置
        self.max_connections = 1000
        self.max_connections_per_group = 100
        
        # 性能监控
        self.global_metrics = {
            "total_connections": 0,
            "active_connections": 0,
            "total_messages": 0,
            "total_bytes": 0,
            "errors": 0,
            "start_time": datetime.now()
        }
        
        # 清理任务
        self.cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
        
        logger.info("WebSocket优化器初始化完成")
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        if not self.cleanup_task or self.cleanup_task.done():
            self.cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def add_connection(
        self, 
        connection_id: str, 
        websocket: WebSocket, 
        group: str = "default"
    ) -> WebSocketConnectionManager:
        """添加连接"""
        
        # 检查连接数限制
        if len(self.connections) >= self.max_connections:
            raise Exception(f"达到最大连接数限制: {self.max_connections}")
        
        if len(self.connection_groups[group]) >= self.max_connections_per_group:
            raise Exception(f"组 {group} 达到最大连接数限制: {self.max_connections_per_group}")
        
        # 创建连接管理器
        manager = WebSocketConnectionManager(connection_id, websocket)
        await manager.start()
        
        # 添加到连接池
        self.connections[connection_id] = manager
        self.connection_groups[group].add(connection_id)
        
        # 更新全局指标
        self.global_metrics["total_connections"] += 1
        self.global_metrics["active_connections"] += 1
        
        logger.info(f"添加WebSocket连接: {connection_id}, 组: {group}")
        return manager
    
    async def remove_connection(self, connection_id: str):
        """移除连接"""
        if connection_id not in self.connections:
            return
        
        manager = self.connections[connection_id]
        
        # 关闭连接
        await manager.close()
        
        # 从连接池移除
        del self.connections[connection_id]
        
        # 从组中移除
        for group_connections in self.connection_groups.values():
            group_connections.discard(connection_id)
        
        # 更新全局指标
        self.global_metrics["active_connections"] -= 1
        
        logger.info(f"移除WebSocket连接: {connection_id}")
    
    async def broadcast_to_group(
        self, 
        group: str, 
        message: Dict[str, Any], 
        exclude: Optional[Set[str]] = None
    ) -> int:
        """向组内广播消息"""
        exclude = exclude or set()
        sent_count = 0
        
        group_connections = self.connection_groups.get(group, set())
        
        for connection_id in group_connections:
            if connection_id in exclude:
                continue
            
            manager = self.connections.get(connection_id)
            if manager and manager.status == ConnectionStatus.CONNECTED:
                success = await manager.send_message(message)
                if success:
                    sent_count += 1
        
        return sent_count
    
    async def broadcast_to_all(
        self, 
        message: Dict[str, Any], 
        exclude: Optional[Set[str]] = None
    ) -> int:
        """向所有连接广播消息"""
        exclude = exclude or set()
        sent_count = 0
        
        for connection_id, manager in self.connections.items():
            if connection_id in exclude:
                continue
            
            if manager.status == ConnectionStatus.CONNECTED:
                success = await manager.send_message(message)
                if success:
                    sent_count += 1
        
        return sent_count
    
    def get_connection_manager(self, connection_id: str) -> Optional[WebSocketConnectionManager]:
        """获取连接管理器"""
        return self.connections.get(connection_id)
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取全局统计"""
        uptime = datetime.now() - self.global_metrics["start_time"]
        
        # 计算总指标
        total_messages = sum(conn.metrics.messages_sent + conn.metrics.messages_received 
                           for conn in self.connections.values())
        total_bytes = sum(conn.metrics.bytes_sent + conn.metrics.bytes_received 
                         for conn in self.connections.values())
        total_errors = sum(conn.metrics.errors for conn in self.connections.values())
        
        return {
            "uptime_seconds": uptime.total_seconds(),
            "total_connections": self.global_metrics["total_connections"],
            "active_connections": self.global_metrics["active_connections"],
            "total_messages": total_messages,
            "total_bytes": total_bytes,
            "total_errors": total_errors,
            "groups": {group: len(connections) for group, connections in self.connection_groups.items()},
            "average_latency": self._calculate_average_latency(),
            "throughput_mps": total_messages / max(uptime.total_seconds(), 1),  # messages per second
        }
    
    def _calculate_average_latency(self) -> float:
        """计算平均延迟"""
        all_latencies = []
        for conn in self.connections.values():
            all_latencies.extend(conn.metrics.latency_samples)
        
        if not all_latencies:
            return 0.0
        
        return sum(all_latencies) / len(all_latencies)
    
    async def _periodic_cleanup(self):
        """定期清理任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                
                # 清理断开的连接
                disconnected = []
                for connection_id, manager in self.connections.items():
                    if manager.status in [ConnectionStatus.DISCONNECTED, ConnectionStatus.ERROR]:
                        disconnected.append(connection_id)
                
                for connection_id in disconnected:
                    await self.remove_connection(connection_id)
                
                if disconnected:
                    logger.info(f"清理了 {len(disconnected)} 个断开的连接")
                
            except Exception as e:
                logger.error(f"定期清理任务错误: {str(e)}")


# 全局优化器实例
websocket_optimizer = WebSocketOptimizer() 