"""
增强LangGraph服务 - 统一整合架构

整合现有的LangGraph组件，提供统一的服务接口：
- 基于现有的enhanced_exercise_graph.py
- 整合状态管理和意图处理
- 提供高级功能扩展接口
- 支持多种工作流模式
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, AsyncGenerator
from datetime import datetime

from .interfaces import StreamingProcessorInterface
from .state_manager import IntegratedStateManager
from .intent_processor import IntegratedIntentProcessor
from .parameter_manager import ParameterManager
from .error_handler import UnifiedErrorHandler
from .performance_monitor import PerformanceMonitor

# 导入现有的LangGraph组件
from ..langgraph.enhanced_exercise_graph import EnhancedExerciseGraph
from ..langgraph.state_definitions import UnifiedFitnessState
from ..langgraph.nodes.intent_router import intent_router_node
from ..langgraph.nodes.parameter_collection import parameter_collection_node
from ..langgraph.nodes.response_generation import response_generation_node

from ...logger.logger import get_logger

logger = get_logger(__name__)


class EnhancedLangGraphService:
    """增强LangGraph服务 - 整合统一架构和原始系统逻辑"""
    
    def __init__(self, db_session, llm_service: Optional = None):
        self.db_session = db_session
        self.llm_service = llm_service
        
        # 整合组件初始化
        self.state_manager = IntegratedStateManager(db_session, llm_service)
        self.intent_processor = IntegratedIntentProcessor(llm_service, db_session)
        self.parameter_manager = ParameterManager(db_session, llm_service)
        self.error_handler = UnifiedErrorHandler()
        self.performance_monitor = PerformanceMonitor()
        
        # 基于现有增强运动图
        self.exercise_graph = EnhancedExerciseGraph(db_session, llm_service)
        
        # 工作流模式配置
        self.workflow_modes = {
            "standard": self._standard_workflow,
            "enhanced": self._enhanced_workflow,
            "streaming": self._streaming_workflow,
            "parameter_collection": self._parameter_collection_workflow,
            "training_plan": self._training_plan_workflow
        }
        
        # 性能配置
        self.config = {
            "max_iterations": 50,
            "timeout": 30,
            "retry_attempts": 3,
            "enable_performance_monitoring": True,
            "enable_error_recovery": True
        }
        
        logger.info("EnhancedLangGraphService 初始化完成")
    
    async def process_request(
        self, 
        state: UnifiedFitnessState, 
        workflow_mode: str = "enhanced",
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """统一请求处理入口"""
        request_id = f"req_{int(time.time() * 1000)}"
        
        try:
            # 性能监控开始
            if self.config["enable_performance_monitoring"]:
                self.performance_monitor.start_request(request_id)
            
            logger.info(f"开始处理请求: {request_id}, 工作流模式: {workflow_mode}")
            
            # 状态预处理
            processed_state = await self.state_manager.prepare_state(state)
            
            # 意图识别和路由
            intent_result = await self.intent_processor.process_intent(processed_state)
            processed_state.update(intent_result)
            
            # 选择工作流模式
            workflow_func = self.workflow_modes.get(workflow_mode, self._enhanced_workflow)
            
            # 执行工作流
            result = await workflow_func(processed_state, config or {})
            
            # 结果后处理
            final_result = await self._post_process_result(result, request_id)
            
            logger.info(f"请求处理完成: {request_id}")
            return final_result
            
        except Exception as e:
            # 统一错误处理
            if self.config["enable_error_recovery"]:
                recovery_result = await self.error_handler.handle_error(e, state, request_id)
                if recovery_result.get("recovered"):
                    return recovery_result["result"]
            
            logger.error(f"请求处理失败: {request_id}, 错误: {str(e)}")
            raise
        
        finally:
            # 性能监控结束
            if self.config["enable_performance_monitoring"]:
                self.performance_monitor.end_request(request_id)
    
    async def stream_process_request(
        self, 
        state: UnifiedFitnessState,
        config: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式处理请求"""
        stream_id = f"stream_{int(time.time() * 1000)}"
        
        try:
            logger.info(f"开始流式处理: {stream_id}")
            
            # 发送开始信号
            yield {
                "type": "stream_start",
                "stream_id": stream_id,
                "timestamp": datetime.now().isoformat()
            }
            
            # 状态预处理
            processed_state = await self.state_manager.prepare_state(state)
            
            # 流式工作流处理
            async for chunk in self._streaming_workflow(processed_state, config or {}):
                yield chunk
            
            # 发送结束信号
            yield {
                "type": "stream_end",
                "stream_id": stream_id,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"流式处理失败: {stream_id}, 错误: {str(e)}")
            yield {
                "type": "stream_error",
                "stream_id": stream_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _standard_workflow(self, state: UnifiedFitnessState, config: Dict[str, Any]) -> Dict[str, Any]:
        """标准工作流 - 基于现有enhanced_exercise_graph"""
        logger.info("执行标准工作流")
        
        # 使用现有的增强运动图
        graph_config = {
            "configurable": {"session_id": state.get("session_id", "default")},
            "recursion_limit": config.get("max_iterations", self.config["max_iterations"])
        }
        
        result = await self.exercise_graph.graph.ainvoke(state, config=graph_config)
        return result
    
    async def _enhanced_workflow(self, state: UnifiedFitnessState, config: Dict[str, Any]) -> Dict[str, Any]:
        """增强工作流 - 多阶段处理"""
        logger.info("执行增强工作流")
        
        # 阶段1: 意图路由
        intent_result = await intent_router_node(state)
        state.update(intent_result)
        
        # 阶段2: 参数收集（如需要）
        if state.get("needs_parameter_collection", False):
            param_result = await parameter_collection_node(state)
            state.update(param_result)
        
        # 阶段3: 核心处理
        core_result = await self._standard_workflow(state, config)
        state.update(core_result)
        
        # 阶段4: 响应生成
        response_result = await response_generation_node(state)
        
        return response_result
    
    async def _streaming_workflow(
        self, 
        state: UnifiedFitnessState, 
        config: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式工作流"""
        logger.info("执行流式工作流")
        
        # 流式配置
        stream_config = {
            "configurable": {"session_id": state.get("session_id", "default")},
            "recursion_limit": config.get("max_iterations", self.config["max_iterations"])
        }
        
        # 使用LangGraph的流式API
        async for chunk in self.exercise_graph.graph.astream(state, config=stream_config):
            # 处理流式块
            processed_chunk = await self._process_stream_chunk(chunk)
            if processed_chunk:
                yield processed_chunk
    
    async def _parameter_collection_workflow(self, state: UnifiedFitnessState, config: Dict[str, Any]) -> Dict[str, Any]:
        """参数收集专用工作流"""
        logger.info("执行参数收集工作流")
        
        # 智能参数收集
        collection_result = await self.parameter_manager.collect_parameters_intelligently(state)
        
        # 如果参数收集完成，继续标准流程
        if collection_result.get("collection_complete", False):
            state.update(collection_result)
            return await self._standard_workflow(state, config)
        
        return collection_result
    
    async def _training_plan_workflow(self, state: UnifiedFitnessState, config: Dict[str, Any]) -> Dict[str, Any]:
        """训练计划生成专用工作流"""
        logger.info("执行训练计划生成工作流")
        
        # 参数验证
        validation_result = await self.parameter_manager.validate_training_parameters(state)
        if not validation_result.get("valid", False):
            return {
                "success": False,
                "error": "训练参数验证失败",
                "validation_errors": validation_result.get("errors", [])
            }
        
        # 使用增强工作流生成训练计划
        state["workflow_type"] = "training_plan_generation"
        return await self._enhanced_workflow(state, config)
    
    async def _process_stream_chunk(self, chunk: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理流式块"""
        if not chunk:
            return None
        
        # 添加时间戳和类型标识
        processed_chunk = {
            "type": "stream_chunk",
            "data": chunk,
            "timestamp": datetime.now().isoformat()
        }
        
        return processed_chunk
    
    async def _post_process_result(self, result: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """结果后处理"""
        
        # 添加元数据
        processed_result = {
            **result,
            "meta": {
                "request_id": request_id,
                "processed_at": datetime.now().isoformat(),
                "service_version": "enhanced_v1.0"
            }
        }
        
        # 性能统计
        if self.config["enable_performance_monitoring"]:
            performance_stats = self.performance_monitor.get_request_stats(request_id)
            if performance_stats:
                processed_result["meta"]["performance"] = performance_stats
        
        return processed_result
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查各组件状态
            components_status = {
                "state_manager": await self._check_component_health(self.state_manager),
                "intent_processor": await self._check_component_health(self.intent_processor),
                "parameter_manager": await self._check_component_health(self.parameter_manager),
                "exercise_graph": await self._check_component_health(self.exercise_graph)
            }
            
            all_healthy = all(status["healthy"] for status in components_status.values())
            
            return {
                "healthy": all_healthy,
                "components": components_status,
                "timestamp": datetime.now().isoformat(),
                "service_version": "enhanced_v1.0"
            }
            
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _check_component_health(self, component) -> Dict[str, Any]:
        """检查组件健康状态"""
        try:
            if hasattr(component, 'health_check'):
                return await component.health_check()
            else:
                # 简单的存在性检查
                return {"healthy": True, "status": "ok"}
        except Exception as e:
            return {"healthy": False, "error": str(e)}
    
    def get_supported_workflows(self) -> List[str]:
        """获取支持的工作流模式"""
        return list(self.workflow_modes.keys())
    
    async def configure_service(self, new_config: Dict[str, Any]) -> None:
        """动态配置服务"""
        self.config.update(new_config)
        logger.info(f"服务配置已更新: {new_config}") 