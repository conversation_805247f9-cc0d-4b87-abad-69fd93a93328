"""
集成模块配置管理

统一管理所有集成模块的配置参数和依赖关系
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)

class ModuleStatus(Enum):
    """模块状态枚举"""
    ENABLED = "enabled"
    DISABLED = "disabled"
    ERROR = "error"
    INITIALIZING = "initializing"

@dataclass
class ModuleConfig:
    """模块配置"""
    name: str
    enabled: bool = True
    required_dependencies: List[str] = field(default_factory=list)
    optional_dependencies: List[str] = field(default_factory=list)
    fallback_enabled: bool = True
    config_params: Dict[str, Any] = field(default_factory=dict)
    status: ModuleStatus = ModuleStatus.DISABLED

class IntegrationConfig:
    """集成配置管理器"""
    
    def __init__(self):
        self.modules = self._initialize_module_configs()
        self.global_config = self._initialize_global_config()
        
    def _initialize_module_configs(self) -> Dict[str, ModuleConfig]:
        """初始化模块配置"""
        return {
            "workflow_orchestrator": ModuleConfig(
                name="工作流编排器",
                enabled=True,
                required_dependencies=["state_adapter", "state_manager"],
                optional_dependencies=["intent_processor", "parameter_manager"],
                fallback_enabled=True,
                config_params={
                    "max_workflow_time": 300,
                    "max_retry_attempts": 3,
                    "stage_timeout": 60
                }
            ),
            "progressive_parameter_collector": ModuleConfig(
                name="渐进式参数收集器",
                enabled=True,
                required_dependencies=["parameter_manager"],
                optional_dependencies=["llm_proxy"],
                fallback_enabled=True,
                config_params={
                    "max_collection_rounds": 5,
                    "collection_timeout": 300
                }
            ),
            "advanced_cache_optimizer": ModuleConfig(
                name="高级缓存优化器",
                enabled=True,
                required_dependencies=["cache_service"],
                optional_dependencies=["redis_client"],
                fallback_enabled=True,
                config_params={
                    "cache_ttl": 3600,
                    "max_cache_size": 1000,
                    "cleanup_interval": 300
                }
            ),
            "performance_monitor": ModuleConfig(
                name="性能监控器",
                enabled=True,
                required_dependencies=[],
                optional_dependencies=["metrics_collector"],
                fallback_enabled=True,
                config_params={
                    "collection_interval": 30,
                    "alert_threshold": 5000,
                    "max_metrics_history": 1000
                }
            ),
            "error_handler": ModuleConfig(
                name="统一错误处理器",
                enabled=True,
                required_dependencies=[],
                optional_dependencies=["logger"],
                fallback_enabled=True,
                config_params={
                    "max_retry_attempts": 3,
                    "retry_delay": 1.0,
                    "alert_enabled": True
                }
            ),
            "streaming_processor": ModuleConfig(
                name="流式处理器",
                enabled=False,  # 默认禁用，按需启用
                required_dependencies=["websocket_manager"],
                optional_dependencies=["async_queue"],
                fallback_enabled=False,
                config_params={
                    "buffer_size": 1024,
                    "timeout": 30,
                    "max_connections": 100
                }
            ),
            "websocket_optimizer": ModuleConfig(
                name="WebSocket优化器",
                enabled=False,  # 默认禁用，按需启用
                required_dependencies=["websocket_manager"],
                optional_dependencies=["connection_pool"],
                fallback_enabled=False,
                config_params={
                    "max_connections": 100,
                    "connection_timeout": 30,
                    "heartbeat_interval": 10
                }
            )
        }
    
    def _initialize_global_config(self) -> Dict[str, Any]:
        """初始化全局配置"""
        return {
            # 初始化配置
            "initialization_timeout": 60,
            "initialization_retry_attempts": 3,
            "initialization_parallel": True,
            
            # 健康检查配置
            "health_check_interval": 60,
            "health_check_timeout": 10,
            "health_check_enabled": True,
            
            # 错误处理配置
            "error_recovery_enabled": True,
            "error_recovery_timeout": 30,
            "fallback_enabled": True,
            
            # 性能配置
            "performance_monitoring_enabled": True,
            "metrics_collection_enabled": True,
            "async_processing_enabled": True,
            
            # 日志配置
            "log_level": "INFO",
            "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "log_to_file": False
        }
    
    def get_module_config(self, module_name: str) -> Optional[ModuleConfig]:
        """获取模块配置"""
        return self.modules.get(module_name)
    
    def is_module_enabled(self, module_name: str) -> bool:
        """检查模块是否启用"""
        config = self.get_module_config(module_name)
        return config.enabled if config else False
    
    def enable_module(self, module_name: str):
        """启用模块"""
        if module_name in self.modules:
            self.modules[module_name].enabled = True
            logger.info(f"模块 {module_name} 已启用")
    
    def disable_module(self, module_name: str):
        """禁用模块"""
        if module_name in self.modules:
            self.modules[module_name].enabled = False
            logger.info(f"模块 {module_name} 已禁用")
    
    def update_module_status(self, module_name: str, status: ModuleStatus):
        """更新模块状态"""
        if module_name in self.modules:
            self.modules[module_name].status = status
            logger.debug(f"模块 {module_name} 状态更新为: {status.value}")
    
    def get_enabled_modules(self) -> List[str]:
        """获取所有启用的模块"""
        return [name for name, config in self.modules.items() if config.enabled]
    
    def get_global_config(self, key: str, default=None):
        """获取全局配置"""
        return self.global_config.get(key, default)
    
    def set_global_config(self, key: str, value: Any):
        """设置全局配置"""
        self.global_config[key] = value
        logger.debug(f"全局配置 {key} 设置为: {value}")
    
    def validate_dependencies(self, module_name: str) -> Dict[str, bool]:
        """验证模块依赖"""
        config = self.get_module_config(module_name)
        if not config:
            return {}
        
        dependency_status = {}
        
        # 检查必需依赖
        for dep in config.required_dependencies:
            dependency_status[dep] = self._check_dependency(dep)
        
        # 检查可选依赖
        for dep in config.optional_dependencies:
            dependency_status[f"{dep}(optional)"] = self._check_dependency(dep)
        
        return dependency_status
    
    def _check_dependency(self, dependency_name: str) -> bool:
        """检查单个依赖是否可用"""
        try:
            # 尝试导入依赖模块
            if dependency_name == "state_adapter":
                from .state_adapter import IntegratedStateAdapter
                return True
            elif dependency_name == "state_manager":
                from .state_manager import IntegratedStateManager
                return True
            elif dependency_name == "cache_service":
                from ..common.cache import default_cache_service
                return True
            elif dependency_name == "llm_proxy":
                from ..llm.proxy import LLMProxy
                return True
            elif dependency_name == "parameter_manager":
                # 参数管理器总是可用（有回退实现）
                return True
            else:
                # 其他依赖的简单检查
                return True
        except ImportError:
            return False
        except Exception:
            return False
    
    def get_module_summary(self) -> Dict[str, Any]:
        """获取模块状态摘要"""
        summary = {
            "total_modules": len(self.modules),
            "enabled_modules": len(self.get_enabled_modules()),
            "modules_by_status": {},
            "dependency_issues": []
        }
        
        # 统计各状态的模块数量
        for status in ModuleStatus:
            count = sum(1 for config in self.modules.values() if config.status == status)
            summary["modules_by_status"][status.value] = count
        
        # 检查依赖问题
        for module_name, config in self.modules.items():
            if config.enabled:
                dependencies = self.validate_dependencies(module_name)
                failed_deps = [dep for dep, available in dependencies.items() 
                              if not available and not dep.endswith("(optional)")]
                if failed_deps:
                    summary["dependency_issues"].append({
                        "module": module_name,
                        "failed_dependencies": failed_deps
                    })
        
        return summary

# 全局配置实例
integration_config = IntegrationConfig()

# 简化的模块检查函数
def is_module_available(module_name: str) -> bool:
    """检查模块是否可用"""
    return integration_config.is_module_enabled(module_name)

def get_module_config_value(module_name: str, config_key: str, default=None):
    """获取模块配置值"""
    config = integration_config.get_module_config(module_name)
    if config:
        return config.config_params.get(config_key, default)
    return default

def update_module_status(module_name: str, status: ModuleStatus):
    """更新模块状态"""
    integration_config.update_module_status(module_name, status) 