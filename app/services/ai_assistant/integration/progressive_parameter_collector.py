"""
渐进式参数收集器

基于现有参数管理器，实现用户体验优化：
- 渐进式参数收集 (Progressive Parameter Collection)
- 智能对话引导 (Intelligent Conversation Guidance)
- 个性化推荐 (Personalized Recommendations)
- 上下文记忆 (Context Memory)
- 用户行为学习 (User Behavior Learning)
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque

from .parameter_manager import ParameterManager
from ..langgraph.state_definitions import UnifiedFitnessState
from ...logger.logger import get_logger

logger = get_logger(__name__)


class CollectionPhase(Enum):
    """收集阶段"""
    GREETING = "greeting"                    # 问候阶段
    BASIC_INFO = "basic_info"               # 基础信息
    DETAILED_PROFILE = "detailed_profile"   # 详细档案
    PREFERENCES = "preferences"             # 偏好设置
    GOAL_SETTING = "goal_setting"           # 目标设置
    VALIDATION = "validation"               # 验证阶段
    COMPLETION = "completion"               # 完成阶段


class ParameterPriority(Enum):
    """参数优先级"""
    CRITICAL = "critical"      # 关键参数，必须收集
    IMPORTANT = "important"    # 重要参数，建议收集
    OPTIONAL = "optional"      # 可选参数，按需收集
    ENHANCEMENT = "enhancement"  # 增强参数，提升体验


@dataclass
class CollectionRule:
    """收集规则"""
    parameter: str
    phase: CollectionPhase
    priority: ParameterPriority
    dependencies: List[str] = field(default_factory=list)  # 依赖参数
    conditions: Dict[str, Any] = field(default_factory=dict)  # 条件
    question_templates: List[str] = field(default_factory=list)  # 问题模板
    validation_rules: Dict[str, Any] = field(default_factory=dict)  # 验证规则
    followup_questions: List[str] = field(default_factory=list)  # 后续问题


@dataclass
class UserContext:
    """用户上下文"""
    user_id: str
    conversation_history: deque = field(default_factory=lambda: deque(maxlen=50))
    collected_parameters: Dict[str, Any] = field(default_factory=dict)
    current_phase: CollectionPhase = CollectionPhase.GREETING
    preferences: Dict[str, Any] = field(default_factory=dict)
    behavioral_patterns: Dict[str, Any] = field(default_factory=dict)
    session_start: datetime = field(default_factory=datetime.now)
    last_interaction: datetime = field(default_factory=datetime.now)
    
    def update_interaction(self):
        """更新交互时间"""
        self.last_interaction = datetime.now()
    
    def add_to_history(self, interaction: Dict[str, Any]):
        """添加到历史记录"""
        self.conversation_history.append({
            **interaction,
            "timestamp": datetime.now().isoformat()
        })


class IntelligentQuestionGenerator:
    """智能问题生成器"""
    
    def __init__(self):
        # 问题模板库
        self.question_templates = {
            # 基础信息模板
            "age": [
                "能告诉我您的年龄吗？这有助于为您制定合适的训练计划。",
                "为了更好地为您服务，请问您今年多大？",
                "您的年龄是多少？这对制定个性化计划很重要。"
            ],
            "gender": [
                "请问您的性别是？（男性/女性）",
                "为了提供更精准的建议，请告诉我您的性别。",
                "您是男性还是女性？这有助于调整训练强度。"
            ],
            "height": [
                "请告诉我您的身高（厘米）。",
                "您的身高是多少？这有助于计算您的BMI。",
                "为了更准确地评估，请提供您的身高信息。"
            ],
            "weight": [
                "您当前的体重是多少公斤？",
                "请告诉我您的体重，这对制定计划很重要。",
                "您的体重是？（单位：千克）"
            ],
            
            # 健身目标模板
            "fitness_goal": [
                "您的主要健身目标是什么？（减脂/增肌/塑形/提升体能）",
                "您希望通过健身达到什么效果？",
                "请选择您最关心的健身目标：减脂、增肌、塑形还是提升体能？"
            ],
            "training_frequency": [
                "您计划每周训练几次？",
                "一周中您有几天可以进行训练？",
                "您理想的训练频率是多少？"
            ],
            "available_time": [
                "您每次训练大约有多长时间？",
                "单次训练您希望控制在多少分钟内？",
                "您每次训练可以投入多长时间？"
            ],
            
            # 经验水平模板
            "fitness_level": [
                "您之前有健身经验吗？（初学者/有一定基础/经验丰富）",
                "请描述一下您的健身水平。",
                "您认为自己的健身基础如何？"
            ],
            
            # 偏好设置模板
            "training_scenario": [
                "您更喜欢在健身房训练还是在家训练？",
                "请选择您的训练环境偏好：健身房或家庭。",
                "您计划在哪里进行训练？"
            ],
            "equipment_preference": [
                "您希望使用什么器械进行训练？",
                "请告诉我您可以使用的健身器械。",
                "您偏好哪种类型的训练器械？"
            ]
        }
        
        # 后续问题模板
        self.followup_templates = {
            "clarification": [
                "能再详细说明一下吗？",
                "可以具体一点吗？",
                "您能提供更多细节吗？"
            ],
            "motivation": [
                "是什么让您想要开始健身的？",
                "您的健身动机是什么？",
                "什么驱动您做出这个选择？"
            ],
            "constraints": [
                "您在训练方面有什么限制或顾虑吗？",
                "有什么因素可能影响您的训练吗？",
                "您需要考虑哪些特殊情况？"
            ]
        }
    
    def generate_question(
        self, 
        parameter: str, 
        context: UserContext,
        style: str = "friendly"
    ) -> str:
        """生成问题"""
        templates = self.question_templates.get(parameter, [])
        
        if not templates:
            return f"请提供您的{parameter}信息。"
        
        # 根据用户历史选择合适的模板
        used_templates = [
            interaction.get("question_template", "") 
            for interaction in context.conversation_history
        ]
        
        # 选择未使用过的模板
        available_templates = [t for t in templates if t not in used_templates]
        if not available_templates:
            available_templates = templates
        
        # 根据风格和上下文调整
        selected = available_templates[0]  # 简化选择逻辑
        
        return self._personalize_question(selected, context, style)
    
    def _personalize_question(
        self, 
        question: str, 
        context: UserContext, 
        style: str
    ) -> str:
        """个性化问题"""
        # 根据用户偏好调整语气
        if style == "casual" and "您" in question:
            question = question.replace("您", "你")
        
        # 添加个人化元素
        if context.collected_parameters.get("name"):
            name = context.collected_parameters["name"]
            if not question.startswith(name):
                question = f"{name}，{question.lower()}"
        
        return question
    
    def generate_followup_question(
        self, 
        category: str, 
        context: UserContext
    ) -> str:
        """生成后续问题"""
        templates = self.followup_templates.get(category, [])
        if templates:
            return templates[0]  # 简化选择逻辑
        return "您还有什么想补充的吗？"


class PersonalizationEngine:
    """个性化引擎"""
    
    def __init__(self):
        self.user_profiles: Dict[str, Dict] = {}
        self.behavioral_models: Dict[str, Dict] = {}
    
    def analyze_user_behavior(self, user_id: str, context: UserContext):
        """分析用户行为"""
        if user_id not in self.behavioral_models:
            self.behavioral_models[user_id] = {
                "response_patterns": defaultdict(int),
                "question_preferences": defaultdict(int),
                "completion_time": [],
                "preferred_detail_level": "medium"
            }
        
        model = self.behavioral_models[user_id]
        
        # 分析响应模式
        for interaction in context.conversation_history[-5:]:  # 最近5次交互
            response = interaction.get("user_response", "")
            if len(response) < 10:
                model["response_patterns"]["brief"] += 1
            elif len(response) > 50:
                model["response_patterns"]["detailed"] += 1
            else:
                model["response_patterns"]["normal"] += 1
        
        # 确定偏好的详细程度
        if model["response_patterns"]["detailed"] > model["response_patterns"]["brief"]:
            model["preferred_detail_level"] = "high"
        elif model["response_patterns"]["brief"] > model["response_patterns"]["detailed"]:
            model["preferred_detail_level"] = "low"
    
    def get_recommendations(
        self, 
        user_id: str, 
        parameter: str, 
        context: UserContext
    ) -> List[str]:
        """获取个性化推荐"""
        recommendations = []
        
        # 基于已收集参数的推荐
        collected = context.collected_parameters
        
        if parameter == "fitness_goal":
            age = collected.get("age")
            if age and age < 25:
                recommendations.extend(["增肌", "体能提升"])
            elif age and age > 40:
                recommendations.extend(["健康维护", "灵活性提升"])
            else:
                recommendations.extend(["减脂", "塑形"])
        
        elif parameter == "training_frequency":
            fitness_level = collected.get("fitness_level")
            if fitness_level == "beginner":
                recommendations.extend(["3次/周", "2次/周"])
            elif fitness_level == "advanced":
                recommendations.extend(["5次/周", "6次/周"])
            else:
                recommendations.extend(["4次/周", "3次/周"])
        
        elif parameter == "available_time":
            fitness_goal = collected.get("fitness_goal")
            if fitness_goal == "weight_loss":
                recommendations.extend(["45分钟", "60分钟"])
            elif fitness_goal == "muscle_gain":
                recommendations.extend(["60分钟", "75分钟"])
            else:
                recommendations.extend(["30分钟", "45分钟"])
        
        return recommendations
    
    def suggest_next_parameters(
        self, 
        context: UserContext
    ) -> List[Tuple[str, ParameterPriority]]:
        """建议下一个要收集的参数"""
        collected = set(context.collected_parameters.keys())
        
        # 定义参数依赖关系
        dependency_map = {
            "height": [],
            "weight": [],
            "age": [],
            "gender": [],
            "fitness_goal": ["age", "gender"],
            "fitness_level": [],
            "training_frequency": ["fitness_goal", "fitness_level"],
            "available_time": ["training_frequency"],
            "training_scenario": ["fitness_goal"],
            "equipment_preference": ["training_scenario"]
        }
        
        suggestions = []
        
        for param, deps in dependency_map.items():
            if param not in collected:
                # 检查依赖是否满足
                if all(dep in collected for dep in deps):
                    # 确定优先级
                    if param in ["age", "gender", "height", "weight"]:
                        priority = ParameterPriority.CRITICAL
                    elif param in ["fitness_goal", "fitness_level"]:
                        priority = ParameterPriority.IMPORTANT
                    else:
                        priority = ParameterPriority.OPTIONAL
                    
                    suggestions.append((param, priority))
        
        # 按优先级排序
        priority_order = {
            ParameterPriority.CRITICAL: 0,
            ParameterPriority.IMPORTANT: 1,
            ParameterPriority.OPTIONAL: 2,
            ParameterPriority.ENHANCEMENT: 3
        }
        
        suggestions.sort(key=lambda x: priority_order[x[1]])
        return suggestions


class ProgressiveParameterCollector:
    """渐进式参数收集器"""
    
    def __init__(self, db_session, llm_service):
        self.db_session = db_session
        self.llm_service = llm_service
        
        # 基础参数管理器
        self.base_parameter_manager = ParameterManager(db_session, llm_service)
        
        # 智能组件
        self.question_generator = IntelligentQuestionGenerator()
        self.personalization_engine = PersonalizationEngine()
        
        # 用户上下文管理
        self.user_contexts: Dict[str, UserContext] = {}
        
        # 收集规则定义
        self.collection_rules = self._init_collection_rules()
        
        # 配置
        self.max_questions_per_session = 5  # 每次会话最多问题数
        self.adaptive_questioning = True    # 自适应提问
        self.learning_enabled = True        # 启用学习
        
        logger.info("渐进式参数收集器初始化完成")
    
    def _init_collection_rules(self) -> Dict[str, CollectionRule]:
        """初始化收集规则"""
        rules = {}
        
        # 基础信息规则
        rules["age"] = CollectionRule(
            parameter="age",
            phase=CollectionPhase.BASIC_INFO,
            priority=ParameterPriority.CRITICAL,
            validation_rules={"min": 12, "max": 100, "type": "int"}
        )
        
        rules["gender"] = CollectionRule(
            parameter="gender",
            phase=CollectionPhase.BASIC_INFO,
            priority=ParameterPriority.CRITICAL,
            validation_rules={"choices": ["male", "female", "男", "女"]}
        )
        
        rules["height"] = CollectionRule(
            parameter="height",
            phase=CollectionPhase.BASIC_INFO,
            priority=ParameterPriority.CRITICAL,
            validation_rules={"min": 100, "max": 250, "type": "float", "unit": "cm"}
        )
        
        rules["weight"] = CollectionRule(
            parameter="weight",
            phase=CollectionPhase.BASIC_INFO,
            priority=ParameterPriority.CRITICAL,
            validation_rules={"min": 30, "max": 300, "type": "float", "unit": "kg"}
        )
        
        # 健身目标规则
        rules["fitness_goal"] = CollectionRule(
            parameter="fitness_goal",
            phase=CollectionPhase.GOAL_SETTING,
            priority=ParameterPriority.IMPORTANT,
            dependencies=["age", "gender"],
            validation_rules={"choices": ["weight_loss", "muscle_gain", "endurance", "strength", "减脂", "增肌", "耐力", "力量"]}
        )
        
        rules["fitness_level"] = CollectionRule(
            parameter="fitness_level",
            phase=CollectionPhase.DETAILED_PROFILE,
            priority=ParameterPriority.IMPORTANT,
            validation_rules={"choices": ["beginner", "intermediate", "advanced", "初学者", "中级", "高级"]}
        )
        
        # 训练偏好规则
        rules["training_frequency"] = CollectionRule(
            parameter="training_frequency",
            phase=CollectionPhase.PREFERENCES,
            priority=ParameterPriority.OPTIONAL,
            dependencies=["fitness_goal", "fitness_level"],
            validation_rules={"min": 1, "max": 7, "type": "int", "unit": "times_per_week"}
        )
        
        rules["available_time"] = CollectionRule(
            parameter="available_time",
            phase=CollectionPhase.PREFERENCES,
            priority=ParameterPriority.OPTIONAL,
            dependencies=["training_frequency"],
            validation_rules={"min": 15, "max": 180, "type": "int", "unit": "minutes"}
        )
        
        rules["training_scenario"] = CollectionRule(
            parameter="training_scenario",
            phase=CollectionPhase.PREFERENCES,
            priority=ParameterPriority.OPTIONAL,
            dependencies=["fitness_goal"],
            validation_rules={"choices": ["gym", "home", "健身房", "家庭"]}
        )
        
        return rules
    
    async def start_collection_session(
        self, 
        user_id: str, 
        state: UnifiedFitnessState
    ) -> Dict[str, Any]:
        """开始收集会话"""
        try:
            # 初始化或获取用户上下文
            if user_id not in self.user_contexts:
                self.user_contexts[user_id] = UserContext(user_id=user_id)
            
            context = self.user_contexts[user_id]
            context.update_interaction()
            
            # 分析用户行为（如果启用学习）
            if self.learning_enabled:
                self.personalization_engine.analyze_user_behavior(user_id, context)
            
            # 确定收集策略
            collection_plan = await self._create_collection_plan(context, state)
            
            logger.info(f"开始参数收集会话: {user_id}, 计划收集 {len(collection_plan)} 个参数")
            
            return {
                "success": True,
                "session_id": f"collect_{user_id}_{int(datetime.now().timestamp())}",
                "collection_plan": collection_plan,
                "current_phase": context.current_phase.value,
                "estimated_questions": len(collection_plan)
            }
            
        except Exception as e:
            logger.error(f"启动收集会话失败: {user_id}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def collect_parameter_interactively(
        self, 
        user_id: str, 
        parameter: Optional[str] = None,
        user_response: Optional[str] = None
    ) -> Dict[str, Any]:
        """交互式参数收集"""
        try:
            if user_id not in self.user_contexts:
                return {
                    "success": False,
                    "error": "会话不存在，请先开始收集会话"
                }
            
            context = self.user_contexts[user_id]
            context.update_interaction()
            
            # 处理用户响应（如果有）
            if user_response and parameter:
                processing_result = await self._process_user_response(
                    context, parameter, user_response
                )
                
                if not processing_result["success"]:
                    return processing_result
            
            # 获取下一个要收集的参数
            next_param_info = await self._get_next_parameter(context)
            
            if not next_param_info:
                # 收集完成
                return await self._finalize_collection(context)
            
            next_param, priority = next_param_info
            
            # 生成问题
            question = self.question_generator.generate_question(
                next_param, 
                context,
                style=context.preferences.get("question_style", "friendly")
            )
            
            # 生成推荐选项
            recommendations = self.personalization_engine.get_recommendations(
                user_id, next_param, context
            )
            
            # 记录交互
            context.add_to_history({
                "type": "question",
                "parameter": next_param,
                "question": question,
                "question_template": question,
                "recommendations": recommendations
            })
            
            return {
                "success": True,
                "next_parameter": next_param,
                "question": question,
                "recommendations": recommendations,
                "priority": priority.value,
                "phase": context.current_phase.value,
                "progress": self._calculate_progress(context),
                "can_skip": priority in [ParameterPriority.OPTIONAL, ParameterPriority.ENHANCEMENT]
            }
            
        except Exception as e:
            logger.error(f"交互式参数收集失败: {user_id}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _process_user_response(
        self, 
        context: UserContext, 
        parameter: str, 
        response: str
    ) -> Dict[str, Any]:
        """处理用户响应"""
        try:
            # 获取验证规则
            rule = self.collection_rules.get(parameter)
            if not rule:
                return {
                    "success": False,
                    "error": f"未知参数: {parameter}"
                }
            
            # 验证响应
            validation_result = await self._validate_response(
                parameter, response, rule.validation_rules
            )
            
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": validation_result["error"],
                    "suggestions": validation_result.get("suggestions", [])
                }
            
            # 保存参数值
            normalized_value = validation_result["normalized_value"]
            context.collected_parameters[parameter] = normalized_value
            
            # 记录到历史
            context.add_to_history({
                "type": "response",
                "parameter": parameter,
                "user_response": response,
                "normalized_value": normalized_value
            })
            
            # 更新阶段
            await self._update_collection_phase(context)
            
            logger.info(f"参数收集成功: {parameter} = {normalized_value}")
            
            return {
                "success": True,
                "parameter": parameter,
                "value": normalized_value
            }
            
        except Exception as e:
            logger.error(f"处理用户响应失败: {parameter}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _validate_response(
        self, 
        parameter: str, 
        response: str, 
        rules: Dict[str, Any]
    ) -> Dict[str, Any]:
        """验证用户响应"""
        try:
            response = response.strip()
            
            if not response:
                return {
                    "valid": False,
                    "error": "请提供有效的回答"
                }
            
            # 类型验证和转换
            if rules.get("type") == "int":
                try:
                    value = int(float(response))  # 支持"25.0"这样的输入
                    
                    # 范围验证
                    if "min" in rules and value < rules["min"]:
                        return {
                            "valid": False,
                            "error": f"值过小，最小值为 {rules['min']}"
                        }
                    if "max" in rules and value > rules["max"]:
                        return {
                            "valid": False,
                            "error": f"值过大，最大值为 {rules['max']}"
                        }
                    
                    return {
                        "valid": True,
                        "normalized_value": value
                    }
                except ValueError:
                    return {
                        "valid": False,
                        "error": "请输入有效的数字"
                    }
            
            elif rules.get("type") == "float":
                try:
                    value = float(response)
                    
                    # 范围验证
                    if "min" in rules and value < rules["min"]:
                        return {
                            "valid": False,
                            "error": f"值过小，最小值为 {rules['min']}"
                        }
                    if "max" in rules and value > rules["max"]:
                        return {
                            "valid": False,
                            "error": f"值过大，最大值为 {rules['max']}"
                        }
                    
                    return {
                        "valid": True,
                        "normalized_value": value
                    }
                except ValueError:
                    return {
                        "valid": False,
                        "error": "请输入有效的数字"
                    }
            
            elif "choices" in rules:
                choices = rules["choices"]
                response_lower = response.lower()
                
                # 精确匹配
                if response in choices:
                    return {
                        "valid": True,
                        "normalized_value": response
                    }
                
                # 模糊匹配
                for choice in choices:
                    if choice.lower() in response_lower or response_lower in choice.lower():
                        return {
                            "valid": True,
                            "normalized_value": choice
                        }
                
                return {
                    "valid": False,
                    "error": f"请选择以下选项之一: {', '.join(choices)}",
                    "suggestions": choices
                }
            
            else:
                # 自由文本
                return {
                    "valid": True,
                    "normalized_value": response
                }
                
        except Exception as e:
            logger.error(f"响应验证失败: {parameter}, 错误: {str(e)}")
            return {
                "valid": False,
                "error": "验证过程出错，请重试"
            }
    
    async def _create_collection_plan(
        self, 
        context: UserContext, 
        state: UnifiedFitnessState
    ) -> List[Dict[str, Any]]:
        """创建收集计划"""
        plan = []
        
        # 获取建议的参数
        suggestions = self.personalization_engine.suggest_next_parameters(context)
        
        # 限制每次会话的问题数量
        limited_suggestions = suggestions[:self.max_questions_per_session]
        
        for param, priority in limited_suggestions:
            rule = self.collection_rules.get(param)
            if rule:
                plan.append({
                    "parameter": param,
                    "priority": priority.value,
                    "phase": rule.phase.value,
                    "estimated_time": self._estimate_question_time(param, context)
                })
        
        return plan
    
    def _estimate_question_time(self, parameter: str, context: UserContext) -> int:
        """估算问题回答时间（秒）"""
        # 根据参数复杂性和用户行为估算
        base_times = {
            "age": 15,
            "gender": 10,
            "height": 15,
            "weight": 15,
            "fitness_goal": 30,
            "fitness_level": 25,
            "training_frequency": 20,
            "available_time": 20,
            "training_scenario": 25
        }
        
        base_time = base_times.get(parameter, 20)
        
        # 根据用户行为调整
        user_model = self.personalization_engine.behavioral_models.get(context.user_id, {})
        if user_model.get("preferred_detail_level") == "high":
            base_time *= 1.5
        elif user_model.get("preferred_detail_level") == "low":
            base_time *= 0.7
        
        return int(base_time)
    
    async def _get_next_parameter(
        self, 
        context: UserContext
    ) -> Optional[Tuple[str, ParameterPriority]]:
        """获取下一个要收集的参数"""
        suggestions = self.personalization_engine.suggest_next_parameters(context)
        
        if not suggestions:
            return None
        
        return suggestions[0]  # 返回优先级最高的
    
    async def _update_collection_phase(self, context: UserContext):
        """更新收集阶段"""
        collected = set(context.collected_parameters.keys())
        
        # 基础信息阶段
        basic_params = {"age", "gender", "height", "weight"}
        if basic_params.issubset(collected) and context.current_phase == CollectionPhase.GREETING:
            context.current_phase = CollectionPhase.BASIC_INFO
        
        # 目标设置阶段
        if {"fitness_goal"}.issubset(collected) and context.current_phase == CollectionPhase.BASIC_INFO:
            context.current_phase = CollectionPhase.GOAL_SETTING
        
        # 详细档案阶段
        if {"fitness_level"}.issubset(collected) and context.current_phase == CollectionPhase.GOAL_SETTING:
            context.current_phase = CollectionPhase.DETAILED_PROFILE
        
        # 偏好设置阶段
        if {"training_frequency"}.issubset(collected) and context.current_phase == CollectionPhase.DETAILED_PROFILE:
            context.current_phase = CollectionPhase.PREFERENCES
    
    def _calculate_progress(self, context: UserContext) -> Dict[str, Any]:
        """计算收集进度"""
        total_critical = sum(1 for rule in self.collection_rules.values() 
                           if rule.priority == ParameterPriority.CRITICAL)
        total_important = sum(1 for rule in self.collection_rules.values() 
                            if rule.priority == ParameterPriority.IMPORTANT)
        
        collected_critical = sum(1 for param in context.collected_parameters.keys()
                               if self.collection_rules.get(param, {}).priority == ParameterPriority.CRITICAL)
        collected_important = sum(1 for param in context.collected_parameters.keys()
                                if self.collection_rules.get(param, {}).priority == ParameterPriority.IMPORTANT)
        
        return {
            "total_collected": len(context.collected_parameters),
            "critical_progress": collected_critical / max(total_critical, 1),
            "important_progress": collected_important / max(total_important, 1),
            "overall_progress": (collected_critical + collected_important) / max(total_critical + total_important, 1),
            "current_phase": context.current_phase.value
        }
    
    async def _finalize_collection(self, context: UserContext) -> Dict[str, Any]:
        """完成收集"""
        try:
            context.current_phase = CollectionPhase.COMPLETION
            
            # 验证收集的参数
            validation_result = await self.base_parameter_manager.validate_training_parameters({
                **context.collected_parameters,
                "session_id": context.user_id
            })
            
            # 保存到数据库
            if validation_result.get("valid", False):
                # TODO: 保存用户档案到数据库
                pass
            
            logger.info(f"参数收集完成: {context.user_id}, 收集了 {len(context.collected_parameters)} 个参数")
            
            return {
                "success": True,
                "collection_complete": True,
                "collected_parameters": context.collected_parameters,
                "validation_result": validation_result,
                "progress": self._calculate_progress(context),
                "session_summary": {
                    "duration": (datetime.now() - context.session_start).total_seconds(),
                    "interactions": len(context.conversation_history),
                    "parameters_collected": len(context.collected_parameters)
                }
            }
            
        except Exception as e:
            logger.error(f"完成收集失败: {context.user_id}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取收集统计"""
        total_sessions = len(self.user_contexts)
        completed_sessions = sum(1 for ctx in self.user_contexts.values() 
                               if ctx.current_phase == CollectionPhase.COMPLETION)
        
        if total_sessions == 0:
            return {
                "total_sessions": 0,
                "completion_rate": 0,
                "average_parameters": 0,
                "average_duration": 0
            }
        
        total_parameters = sum(len(ctx.collected_parameters) for ctx in self.user_contexts.values())
        total_duration = sum((ctx.last_interaction - ctx.session_start).total_seconds() 
                           for ctx in self.user_contexts.values())
        
        return {
            "total_sessions": total_sessions,
            "completed_sessions": completed_sessions,
            "completion_rate": completed_sessions / total_sessions,
            "average_parameters": total_parameters / total_sessions,
            "average_duration": total_duration / total_sessions,
            "active_sessions": sum(1 for ctx in self.user_contexts.values() 
                                 if ctx.current_phase != CollectionPhase.COMPLETION)
        } 