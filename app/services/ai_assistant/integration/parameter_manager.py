"""
增强参数管理器

基于现有的参数提取器和图节点，整合参数收集和验证功能：
- 复用现有的ParameterExtractor和EnhancedParameterExtractor
- 整合现有的param_collector_node和user_info_collector_node
- 提供统一的参数收集和验证接口

实现参数收集完整性100%的目标。
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from .interfaces import ParameterManagerInterface
from .state_adapter import IntegratedStateAdapter
from ..langgraph.state_definitions import UnifiedFitnessState

# 导入现有的参数提取器（如果不存在则创建简化实现）
try:
    from ..parameter.extractor import ParameterExtractor
except ImportError:
    ParameterExtractor = None

# 导入日志
import logging

logger = logging.getLogger(__name__)


class ParameterValidator:
    """参数验证器 - 基于现有系统的验证规则"""
    
    def __init__(self):
        self.validation_rules = self._init_validation_rules()
    
    def _init_validation_rules(self) -> Dict[str, Dict]:
        """初始化验证规则 - 基于现有系统的字段要求"""
        return {
            "user_profile": {
                "age": {"type": int, "required": True, "min": 12, "max": 100},
                "gender": {"type": str, "required": True, "choices": ["male", "female"]},
                "height": {"type": (int, float), "required": True, "min": 100, "max": 250},
                "weight": {"type": (int, float), "required": True, "min": 30, "max": 300},
                "fitness_level": {"type": str, "required": False, "choices": ["beginner", "intermediate", "advanced"]},
                "fitness_goal": {"type": str, "required": False, "choices": ["weight_loss", "muscle_gain", "endurance", "strength"]}
            },
            "training_params": {
                "body_part": {"type": str, "required": True, "choices": ["胸部", "背部", "肩部", "手臂", "腿部", "臀部", "核心", "全身"]},
                "scenario": {"type": str, "required": True, "choices": ["健身房", "家庭"]},
                "plan_type": {"type": str, "required": False, "choices": ["单日", "周计划", "月计划"]},
                "training_days": {"type": int, "required": False, "min": 1, "max": 7},
                "available_time": {"type": int, "required": False, "min": 15, "max": 180},
                "equipment_available": {"type": list, "required": False},
                "training_goal": {"type": str, "required": False, "choices": ["增肌", "减脂", "力量", "耐力", "塑形"]}
            }
        }
    
    def validate_user_profile(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """验证用户档案"""
        return self._validate_params(profile, "user_profile")
    
    def validate_training_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证训练参数"""
        return self._validate_params(params, "training_params")
    
    def _validate_params(self, params: Dict[str, Any], param_type: str) -> Dict[str, Any]:
        """通用参数验证"""
        rules = self.validation_rules.get(param_type, {})
        validated_params = {}
        errors = []
        
        for field, rule in rules.items():
            value = params.get(field)
            
            # 检查必需字段
            if rule.get("required", False) and (value is None or value == ""):
                errors.append(f"{field}为必需字段")
                continue
            
            if value is not None:
                # 类型检查和转换
                expected_type = rule.get("type")
                if expected_type and not isinstance(value, expected_type):
                    try:
                        if expected_type == int:
                            value = int(value)
                        elif expected_type == float:
                            value = float(value)
                        elif expected_type == str:
                            value = str(value)
                    except (ValueError, TypeError):
                        errors.append(f"{field}类型错误，期望{expected_type}")
                        continue
                
                # 范围检查
                if "min" in rule and value < rule["min"]:
                    errors.append(f"{field}不能小于{rule['min']}")
                    continue
                if "max" in rule and value > rule["max"]:
                    errors.append(f"{field}不能大于{rule['max']}")
                    continue
                
                # 选择值检查
                if "choices" in rule and value not in rule["choices"]:
                    errors.append(f"{field}必须为{rule['choices']}中的一个")
                    continue
                
                validated_params[field] = value
        
        if errors:
            raise ValueError(f"参数验证失败: {'; '.join(errors)}")
        
        return validated_params


class EnhancedParameterManager(ParameterManagerInterface):
    """增强参数管理器 - 基于现有参数提取器，整合参数管理逻辑"""
    
    def __init__(self, db_session, llm_service):
        # 数据库会话和服务
        self.db_session = db_session
        self.llm_service = llm_service
        
        # 使用现有的参数提取器（如果可用）
        if ParameterExtractor:
            self.parameter_extractor = ParameterExtractor()
        else:
            self.parameter_extractor = None
            logger.warning("ParameterExtractor不可用，使用简化实现")
        
        # 使用简化的管理器实现
        self.training_param_manager = self._create_simple_training_manager()
        self.user_profile_manager = self._create_simple_profile_manager()
        
        # 参数验证器
        self.parameter_validator = ParameterValidator()
        
        # 配置参数 - 基于现有系统配置
        self.max_collection_rounds = 5  # 最大收集轮数
        self.collection_timeout = 300   # 收集超时时间（秒）
        
        # 必需字段定义 - 基于现有系统的字段要求
        self.required_user_fields = ["age", "gender", "height", "weight"]
        self.required_training_fields = {
            "training_plan": ["body_part", "scenario"],
            "exercise_recommendation": ["body_part", "scenario"],
            "fitness_advice": ["body_part"],
            "daily_workout_plan": ["body_part", "scenario"],
            "weekly_workout_plan": ["body_part", "scenario", "plan_type"]
        }
        
        # 状态适配器
        self.state_adapter = IntegratedStateAdapter()
        
        logger.info("增强参数管理器初始化完成")
    
    def _create_simple_training_manager(self):
        """创建简化的训练参数管理器"""
        class SimpleTrainingManager:
            def __init__(self, db_session):
                self.db_session = db_session
            
            async def check_missing_params(self, params: Dict[str, Any], intent: str) -> List[str]:
                """检查缺失的参数"""
                required_fields = ["body_part", "scenario"]
                missing = []
                for field in required_fields:
                    if field not in params or not params[field]:
                        missing.append(field)
                return missing
            
            async def generate_param_prompt(self, param_name: str, existing_params: Dict[str, Any]) -> str:
                """生成参数收集提示"""
                prompts = {
                    "body_part": "您想训练哪个部位？例如：胸部、背部、腿部等",
                    "scenario": "您计划在哪里训练？健身房还是家里？",
                    "plan_type": "您需要单日训练计划还是周计划？"
                }
                return prompts.get(param_name, f"请提供{param_name}的信息")
        
        return SimpleTrainingManager(self.db_session)
    
    def _create_simple_profile_manager(self):
        """创建简化的用户信息管理器"""
        class SimpleProfileManager:
            def __init__(self, db_session):
                self.db_session = db_session
            
            async def check_missing_fields(self, profile: Dict[str, Any]) -> List[str]:
                """检查缺失的字段"""
                required_fields = ["age", "gender", "height", "weight"]
                missing = []
                for field in required_fields:
                    if field not in profile or not profile[field]:
                        missing.append(field)
                return missing
            
            async def generate_collection_prompt(self, field_name: str, existing_profile: Dict[str, Any]) -> str:
                """生成收集提示"""
                prompts = {
                    "age": "请告诉我您的年龄",
                    "gender": "请告诉我您的性别（男/女）",
                    "height": "请告诉我您的身高（厘米）",
                    "weight": "请告诉我您的体重（公斤）"
                }
                return prompts.get(field_name, f"请提供您的{field_name}")
            
            async def extract_user_info(self, message: str, field_name: str, existing_profile: Dict[str, Any]) -> Any:
                """从消息中提取用户信息"""
                import re
                
                if field_name == "age":
                    age_match = re.search(r'(\d+)', message)
                    if age_match:
                        return int(age_match.group(1))
                elif field_name == "gender":
                    if any(word in message for word in ["男", "male", "先生", "男性"]):
                        return "male"
                    elif any(word in message for word in ["女", "female", "女士", "女性"]):
                        return "female"
                elif field_name in ["height", "weight"]:
                    num_match = re.search(r'(\d+(?:\.\d+)?)', message)
                    if num_match:
                        return float(num_match.group(1))
                
                return None
            
            async def generate_retry_prompt(self, field_name: str, error_msg: str) -> str:
                """生成重试提示"""
                return f"抱歉，{error_msg}。请重新提供您的{field_name}信息。"
        
        return SimpleProfileManager(self.db_session)
    
    async def collect_user_info(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """收集用户信息 - 使用现有系统的成熟逻辑"""
        try:
            logger.info(f"开始收集用户信息: {state['conversation_id']}")
            
            # 检查是否已在收集流程中
            flow_state = state.get("flow_state", {})
            if flow_state.get("collecting_user_info"):
                return await self._continue_user_info_collection(state)
            
            # 检查用户信息完整性
            missing_fields = await self.user_profile_manager.check_missing_fields(
                state["user_profile"]
            )
            
            if missing_fields:
                # 开始用户信息收集流程
                return await self._start_user_info_collection(state, missing_fields)
            else:
                # 用户信息完整，继续处理
                state["flow_state"]["user_info_complete"] = True
                state["response_content"] = "用户信息已完整，可以继续为您制定健身计划。"
                logger.info(f"用户信息已完整: {state['conversation_id']}")
            
            return state
            
        except Exception as e:
            logger.error(f"用户信息收集失败: {str(e)}")
            return await self._fallback_user_info_collection(state)
    
    async def collect_training_params(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """收集训练参数 - 使用现有系统的智能提取"""
        try:
            logger.info(f"开始收集训练参数: {state['conversation_id']}")
            
            # 检查是否已在收集流程中
            flow_state = state.get("flow_state", {})
            if flow_state.get("collecting_training_params"):
                return await self._continue_training_params_collection(state)
            
            # 从消息中提取参数
            latest_message = state["messages"][-1].content if state["messages"] else ""
            
            # 使用现有的参数提取器
            extracted_params = await self.parameter_extractor.extract_parameters(
                latest_message, {"training_params": state["training_params"]}
            )
            
            # 验证和标准化参数
            try:
                validated_params = self.parameter_validator.validate_training_params(
                    extracted_params
                )
                # 更新状态
                state["training_params"].update(validated_params)
                logger.debug(f"参数验证成功: {validated_params}")
            except ValueError as e:
                logger.warning(f"参数验证失败: {str(e)}")
                # 保留原始参数，但记录验证错误
                state["training_params"].update(extracted_params)
                state["flow_state"]["param_validation_errors"] = str(e)
            
            # 检查参数完整性
            missing_params = await self.training_param_manager.check_missing_params(
                state["training_params"], state["intent"]
            )
            
            if missing_params:
                # 开始训练参数收集流程
                return await self._start_training_params_collection(state, missing_params)
            else:
                # 参数收集完成
                state["flow_state"]["training_params_complete"] = True
                state["response_content"] = "训练参数收集完成，正在为您制定个性化健身计划..."
                logger.info(f"训练参数收集完成: {state['conversation_id']}")
            
            return state
            
        except Exception as e:
            logger.error(f"训练参数收集失败: {str(e)}")
            return await self._fallback_param_collection(state)

    async def validate_parameters(self, params: Dict[str, Any], intent: str) -> Dict[str, Any]:
        """验证参数完整性"""
        try:
            # 根据意图类型验证不同的参数
            if intent in ["training_plan", "exercise_recommendation"]:
                return self.parameter_validator.validate_training_params(params)
            else:
                # 对于其他意图，进行基础验证
                validated = {}
                for key, value in params.items():
                    if value is not None and value != "":
                        validated[key] = value
                return validated
        except Exception as e:
            logger.error(f"参数验证失败: {str(e)}")
            return params

    async def _start_user_info_collection(self, state: UnifiedFitnessState, missing_fields: List[str]) -> UnifiedFitnessState:
        """开始用户信息收集流程"""
        try:
            # 设置收集状态
            state["flow_state"]["collecting_user_info"] = True
            state["flow_state"]["missing_fields"] = missing_fields
            state["flow_state"]["current_field"] = missing_fields[0]
            state["flow_state"]["collection_start_time"] = time.time()
            state["flow_state"]["collection_round"] = 1

            # 生成收集提示
            collection_prompt = await self.user_profile_manager.generate_collection_prompt(
                missing_fields[0], state["user_profile"]
            )

            state["response_content"] = collection_prompt
            state["response_type"] = "user_info_collection"

            logger.info(f"开始收集用户信息: {missing_fields[0]}")
            return state

        except Exception as e:
            logger.error(f"开始用户信息收集失败: {str(e)}")
            raise

    async def _continue_user_info_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """继续用户信息收集流程"""
        try:
            flow_state = state["flow_state"]
            current_field = flow_state.get("current_field")
            missing_fields = flow_state.get("missing_fields", [])

            # 检查超时
            collection_start_time = flow_state.get("collection_start_time", time.time())
            if time.time() - collection_start_time > self.collection_timeout:
                logger.warning("用户信息收集超时")
                return await self._timeout_user_info_collection(state)

            # 从最新消息中提取用户信息
            latest_message = state["messages"][-1].content if state["messages"] else ""
            extracted_info = await self.user_profile_manager.extract_user_info(
                latest_message, current_field, state["user_profile"]
            )

            if extracted_info:
                # 验证提取的信息
                try:
                    validated_info = self.parameter_validator.validate_user_profile(
                        {current_field: extracted_info}
                    )
                    state["user_profile"].update(validated_info)
                    logger.info(f"成功收集用户信息: {current_field} = {extracted_info}")

                    # 移除已收集的字段
                    if current_field in missing_fields:
                        missing_fields.remove(current_field)

                    # 检查是否还有缺失字段
                    if missing_fields:
                        # 继续收集下一个字段
                        next_field = missing_fields[0]
                        flow_state["current_field"] = next_field
                        flow_state["collection_round"] += 1

                        collection_prompt = await self.user_profile_manager.generate_collection_prompt(
                            next_field, state["user_profile"]
                        )
                        state["response_content"] = collection_prompt

                        logger.info(f"继续收集用户信息: {next_field}")
                    else:
                        # 收集完成
                        flow_state["collecting_user_info"] = False
                        flow_state["user_info_complete"] = True
                        state["response_content"] = "用户信息收集完成！现在为您制定个性化健身计划。"
                        logger.info("用户信息收集完成")

                except ValueError as e:
                    # 验证失败，重新收集
                    logger.warning(f"用户信息验证失败: {str(e)}")
                    retry_prompt = await self.user_profile_manager.generate_retry_prompt(
                        current_field, str(e)
                    )
                    state["response_content"] = retry_prompt
                    flow_state["collection_round"] += 1

            else:
                # 未能提取到有效信息，重新询问
                flow_state["collection_round"] += 1
                if flow_state["collection_round"] > self.max_collection_rounds:
                    logger.warning("用户信息收集达到最大轮数")
                    return await self._timeout_user_info_collection(state)

                retry_prompt = await self.user_profile_manager.generate_retry_prompt(
                    current_field, "未能识别您的回答"
                )
                state["response_content"] = retry_prompt

            return state

        except Exception as e:
            logger.error(f"继续用户信息收集失败: {str(e)}")
            return await self._fallback_user_info_collection(state)

    async def _start_training_params_collection(self, state: UnifiedFitnessState, missing_params: List[str]) -> UnifiedFitnessState:
        """开始训练参数收集流程"""
        try:
            # 设置收集状态
            state["flow_state"]["collecting_training_params"] = True
            state["flow_state"]["missing_params"] = missing_params
            state["flow_state"]["current_param"] = missing_params[0]
            state["flow_state"]["param_collection_start_time"] = time.time()
            state["flow_state"]["param_collection_round"] = 1

            # 生成参数收集提示
            param_prompt = await self.training_param_manager.generate_param_prompt(
                missing_params[0], state["training_params"]
            )

            state["response_content"] = param_prompt
            state["response_type"] = "training_params_collection"

            logger.info(f"开始收集训练参数: {missing_params[0]}")
            return state

        except Exception as e:
            logger.error(f"开始训练参数收集失败: {str(e)}")
            raise

    async def _continue_training_params_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """继续训练参数收集流程"""
        try:
            flow_state = state["flow_state"]
            current_param = flow_state.get("current_param")
            missing_params = flow_state.get("missing_params", [])

            # 检查超时
            param_collection_start_time = flow_state.get("param_collection_start_time", time.time())
            if time.time() - param_collection_start_time > self.collection_timeout:
                logger.warning("训练参数收集超时")
                return await self._timeout_param_collection(state)

            # 从最新消息中提取参数
            latest_message = state["messages"][-1].content if state["messages"] else ""

            # 使用现有的参数提取器
            extracted_params = await self.parameter_extractor.extract_parameters(
                latest_message, {"training_params": state["training_params"]}
            )

            # 检查是否提取到当前参数
            if current_param in extracted_params and extracted_params[current_param]:
                try:
                    # 验证参数
                    validated_param = self.parameter_validator.validate_training_params(
                        {current_param: extracted_params[current_param]}
                    )
                    state["training_params"].update(validated_param)
                    logger.info(f"成功收集训练参数: {current_param} = {extracted_params[current_param]}")

                    # 移除已收集的参数
                    if current_param in missing_params:
                        missing_params.remove(current_param)

                    # 检查是否还有缺失参数
                    if missing_params:
                        # 继续收集下一个参数
                        next_param = missing_params[0]
                        flow_state["current_param"] = next_param
                        flow_state["param_collection_round"] += 1

                        param_prompt = await self.training_param_manager.generate_param_prompt(
                            next_param, state["training_params"]
                        )
                        state["response_content"] = param_prompt

                        logger.info(f"继续收集训练参数: {next_param}")
                    else:
                        # 收集完成
                        flow_state["collecting_training_params"] = False
                        flow_state["training_params_complete"] = True
                        state["response_content"] = "训练参数收集完成！正在为您制定个性化健身计划..."
                        logger.info("训练参数收集完成")

                except ValueError as e:
                    # 验证失败，重新收集
                    logger.warning(f"训练参数验证失败: {str(e)}")
                    retry_prompt = f"抱歉，{str(e)}。请重新提供{current_param}的信息。"
                    state["response_content"] = retry_prompt
                    flow_state["param_collection_round"] += 1

            else:
                # 未能提取到有效参数，重新询问
                flow_state["param_collection_round"] += 1
                if flow_state["param_collection_round"] > self.max_collection_rounds:
                    logger.warning("训练参数收集达到最大轮数")
                    return await self._timeout_param_collection(state)

                retry_prompt = f"抱歉，未能识别您的{current_param}信息。请重新提供。"
                state["response_content"] = retry_prompt

            return state

        except Exception as e:
            logger.error(f"继续训练参数收集失败: {str(e)}")
            return await self._fallback_param_collection(state)

    async def _fallback_user_info_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """用户信息收集降级处理"""
        state["flow_state"]["collecting_user_info"] = False
        state["flow_state"]["user_info_complete"] = False
        state["response_content"] = "用户信息收集遇到问题，我们将使用默认设置继续为您服务。"
        state["error_count"] = state.get("error_count", 0) + 1
        return state

    async def _fallback_param_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """训练参数收集降级处理"""
        state["flow_state"]["collecting_training_params"] = False
        state["flow_state"]["training_params_complete"] = False
        state["response_content"] = "参数收集遇到问题，我们将使用默认设置为您制定训练计划。"
        state["error_count"] = state.get("error_count", 0) + 1
        return state

    async def _timeout_user_info_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """用户信息收集超时处理"""
        state["flow_state"]["collecting_user_info"] = False
        state["response_content"] = "用户信息收集超时，我们将使用已有信息继续为您服务。"
        logger.warning(f"用户信息收集超时: {state['conversation_id']}")
        return state

    async def _timeout_param_collection(self, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """训练参数收集超时处理"""
        state["flow_state"]["collecting_training_params"] = False
        state["response_content"] = "参数收集超时，我们将使用已有参数为您制定训练计划。"
        logger.warning(f"训练参数收集超时: {state['conversation_id']}")
        return state
