"""
整合意图处理器

结合三套系统的意图处理优势：
- 原始系统的上下文感知意图识别
- LangGraph的智能路由决策
- 统一架构的专家节点处理

实现三层意图处理策略：识别 -> 路由 -> 处理
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

from .interfaces import IntentProcessorInterface, IntentResult
from .state_adapter import IntegratedStateAdapter
from ..langgraph.state_definitions import UnifiedFitnessState
import logging

# 创建简单的意图识别器类
class IntentRecognizer:
    def __init__(self, llm_service):
        self.llm_service = llm_service

    async def recognize_intent(self, message: str, context: Dict[str, Any]) -> IntentResult:
        """简单的意图识别实现"""
        # 基于关键词的简单识别
        message_lower = message.lower()

        if any(keyword in message_lower for keyword in ["训练计划", "健身计划", "锻炼计划", "制定计划"]):
            return IntentResult("training_plan", 0.9, {"body_part": "general"})
        elif any(keyword in message_lower for keyword in ["推荐", "动作", "运动", "练习"]):
            return IntentResult("exercise_recommendation", 0.85, {"type": "general"})
        elif any(keyword in message_lower for keyword in ["怎么", "如何", "为什么", "什么是"]):
            return IntentResult("fitness_qa", 0.8, {"topic": "general"})
        elif any(keyword in message_lower for keyword in ["饮食", "吃", "营养", "食物"]):
            return IntentResult("diet_advice", 0.8, {"topic": "nutrition"})
        else:
            return IntentResult("general_chat", 0.6, {})

logger = logging.getLogger(__name__)


class IntegratedIntentProcessor(IntentProcessorInterface):
    """整合意图处理器 - 结合三套系统的意图处理优势"""
    
    def __init__(self, llm_service, db_session):
        # 原始系统的意图识别器
        self.original_recognizer = IntentRecognizer(llm_service)
        
        # 数据库会话
        self.db_session = db_session
        self.llm_service = llm_service
        
        # 专家节点映射 - 基于现有图节点系统
        self.expert_nodes = self._init_expert_nodes()
        
        # 意图置信度阈值
        self.confidence_threshold = 0.7
        self.fallback_intent = "general_chat"
        
        # 上下文管理
        self.context_window_size = 5  # 保留最近5轮对话作为上下文
        
        # 状态适配器
        self.state_adapter = IntegratedStateAdapter()
        
        logger.info("整合意图处理器初始化完成")
    
    def _init_expert_nodes(self) -> Dict[str, Any]:
        """初始化专家节点映射 - 基于现有图节点系统"""
        try:
            # 导入现有的图节点
            from ...graph_nodes.training_plan_expert_node import training_plan_expert_node
            from ...graph_nodes.fitness_qa_expert_node import fitness_qa_expert_node
            from ...graph_nodes.exercise_recommendation_expert_node import exercise_recommendation_expert_node
            from ...graph_nodes.general_chat_expert_node import general_chat_expert_node
            
            return {
                "training_plan": training_plan_expert_node,
                "exercise_recommendation": exercise_recommendation_expert_node,
                "fitness_qa": fitness_qa_expert_node,
                "general_chat": general_chat_expert_node,
                "diet_advice": general_chat_expert_node,  # 暂时使用通用聊天
                "fitness_advice": fitness_qa_expert_node
            }
        except ImportError as e:
            logger.warning(f"导入图节点失败，使用模拟节点: {str(e)}")
            return {
                "training_plan": self._mock_expert_node,
                "exercise_recommendation": self._mock_expert_node,
                "fitness_qa": self._mock_expert_node,
                "general_chat": self._mock_expert_node,
                "diet_advice": self._mock_expert_node,
                "fitness_advice": self._mock_expert_node
            }
    
    async def process_intent(self, message: str, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """整合意图处理流程 - 三层处理策略"""
        try:
            logger.info(f"开始意图处理: {state['conversation_id']}")
            processing_start_time = time.time()
            
            # 1. 使用原始系统的上下文感知意图识别
            intent_result = await self._recognize_intent_with_context(message, state)
            
            # 2. 更新状态中的意图信息
            state["intent"] = intent_result["intent"]
            state["confidence"] = intent_result["confidence"]
            state["intent_parameters"] = intent_result["parameters"]
            
            # 3. 判断是否需要智能路由
            if intent_result["confidence"] >= self.confidence_threshold:
                # 高置信度，使用原始识别结果
                target_processor = intent_result["intent"]
                logger.info(f"高置信度意图识别: {target_processor} (置信度: {intent_result['confidence']})")
            else:
                # 低置信度，使用智能路由决策
                target_processor = await self._intelligent_routing_decision(message, state)
                logger.info(f"智能路由决策: {target_processor}")
            
            # 4. 调用对应的专家节点处理
            if target_processor in self.expert_nodes:
                expert_result = await self._call_expert_node(target_processor, state)
                state.update(expert_result)
                logger.info(f"专家节点处理完成: {target_processor}")
            else:
                # 降级到通用聊天处理
                logger.warning(f"未知意图类型，降级处理: {target_processor}")
                fallback_result = await self._call_expert_node(self.fallback_intent, state)
                state.update(fallback_result)
            
            # 5. 记录处理路径和性能指标
            processing_time = time.time() - processing_start_time
            processing_path = state.get("processing_path", [])
            processing_path.append(f"intent_processor -> {target_processor}")
            state["processing_path"] = processing_path
            state["node_execution_times"]["intent_processor"] = processing_time
            
            logger.info(f"意图处理完成: {state['conversation_id']}, 用时: {processing_time:.2f}s")
            return state
            
        except Exception as e:
            logger.error(f"意图处理失败: {str(e)}")
            # 降级处理
            return await self._fallback_intent_processing(message, state)
    
    async def recognize_intent(self, message: str, context: Dict[str, Any]) -> IntentResult:
        """识别用户意图 - 独立意图识别接口"""
        try:
            # 构建识别上下文
            recognition_context = {
                "conversation_id": context.get("conversation_id", ""),
                "user_info": context.get("user_profile", {}),
                "training_params": context.get("training_params", {}),
                "flow_state": context.get("flow_state", {}),
                "recent_messages": self._extract_recent_messages(context.get("messages", []))
            }
            
            # 调用原始系统的意图识别
            intent_result = await self.original_recognizer.recognize_intent(
                message, recognition_context
            )
            
            return intent_result
            
        except Exception as e:
            logger.error(f"意图识别失败: {str(e)}")
            # 返回默认意图结果
            return IntentResult(
                intent=self.fallback_intent,
                confidence=0.5,
                parameters={},
                sub_intents=[],
                recognition_time=0,
                error=str(e)
            )
    
    async def route_to_handler(self, intent: str, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """路由到对应的处理器"""
        try:
            if intent in self.expert_nodes:
                expert_result = await self._call_expert_node(intent, state)
                state.update(expert_result)
            else:
                # 使用默认处理器
                fallback_result = await self._call_expert_node(self.fallback_intent, state)
                state.update(fallback_result)
            
            return state
        except Exception as e:
            logger.error(f"路由处理失败: {str(e)}")
            return state
    
    async def _recognize_intent_with_context(self, message: str, state: UnifiedFitnessState) -> Dict[str, Any]:
        """带上下文的意图识别"""
        context = self._extract_context(state)
        intent_result = await self.recognize_intent(message, context)
        
        return {
            "intent": intent_result.intent,
            "confidence": intent_result.confidence,
            "parameters": intent_result.parameters,
            "sub_intents": getattr(intent_result, 'sub_intents', []),
            "recognition_time": getattr(intent_result, 'recognition_time', 0)
        }
    
    async def _intelligent_routing_decision(self, message: str, state: UnifiedFitnessState) -> str:
        """智能路由决策 - 基于多维度分析"""
        try:
            # 导入智能路由节点
            from ...graph_nodes.router_node import router_node
            
            # 调用智能路由节点
            routing_result = await router_node(state)
            
            # 提取路由决策
            if "next_node" in routing_result:
                return routing_result["next_node"]
            elif "goto" in routing_result:
                return routing_result["goto"]
            else:
                return self.fallback_intent
                
        except Exception as e:
            logger.error(f"智能路由决策失败: {str(e)}")
            # 基于关键词的简单路由
            return self._keyword_based_routing(message)
    
    def _keyword_based_routing(self, message: str) -> str:
        """基于关键词的简单路由"""
        message_lower = message.lower()
        
        # 训练计划相关
        if any(keyword in message_lower for keyword in ["训练计划", "健身计划", "锻炼计划", "制定计划"]):
            return "training_plan"
        
        # 运动推荐相关
        if any(keyword in message_lower for keyword in ["推荐", "动作", "运动", "练习"]):
            return "exercise_recommendation"
        
        # 健身问答相关
        if any(keyword in message_lower for keyword in ["怎么", "如何", "为什么", "什么是"]):
            return "fitness_qa"
        
        # 饮食建议相关
        if any(keyword in message_lower for keyword in ["饮食", "吃", "营养", "食物"]):
            return "diet_advice"
        
        # 默认通用聊天
        return self.fallback_intent
    
    async def _call_expert_node(self, expert_type: str, state: UnifiedFitnessState) -> Dict[str, Any]:
        """调用专家节点"""
        try:
            expert_node = self.expert_nodes.get(expert_type)
            if expert_node:
                # 调用专家节点
                result = await expert_node(state)
                return result if isinstance(result, dict) else {}
            else:
                logger.warning(f"未找到专家节点: {expert_type}")
                return {}
        except Exception as e:
            logger.error(f"调用专家节点失败: {expert_type}, 错误: {str(e)}")
            return {}
    
    async def _mock_expert_node(self, state: UnifiedFitnessState) -> Dict[str, Any]:
        """模拟专家节点 - 用于测试"""
        return {
            "response_content": f"这是一个模拟响应，处理意图: {state.get('intent', 'unknown')}",
            "response_type": "text",
            "structured_data": {},
            "current_node": "mock_expert"
        }
    
    def _extract_context(self, state: UnifiedFitnessState) -> Dict[str, Any]:
        """提取上下文信息供原始系统使用"""
        return {
            "conversation_id": state["conversation_id"],
            "user_profile": state["user_profile"],
            "training_params": state["training_params"],
            "flow_state": state["flow_state"],
            "messages": state["messages"]
        }
    
    def _extract_recent_messages(self, messages: List) -> List[Dict]:
        """提取最近的消息作为上下文"""
        try:
            recent_messages = []
            for msg in messages[-self.context_window_size:]:
                if hasattr(msg, 'content'):
                    recent_messages.append({
                        "role": "user" if "Human" in str(type(msg)) else "assistant",
                        "content": msg.content
                    })
                elif isinstance(msg, dict):
                    recent_messages.append(msg)
            return recent_messages
        except Exception as e:
            logger.error(f"提取消息上下文失败: {str(e)}")
            return []
    
    async def _fallback_intent_processing(self, message: str, state: UnifiedFitnessState) -> UnifiedFitnessState:
        """降级意图处理"""
        try:
            logger.info(f"执行降级意图处理: {state['conversation_id']}")
            
            # 设置默认意图
            state["intent"] = self.fallback_intent
            state["confidence"] = 0.5
            state["intent_parameters"] = {}
            
            # 调用通用聊天专家
            fallback_result = await self._call_expert_node(self.fallback_intent, state)
            state.update(fallback_result)
            
            # 记录降级处理
            processing_path = state.get("processing_path", [])
            processing_path.append("intent_processor -> fallback -> general_chat")
            state["processing_path"] = processing_path
            state["error_count"] = state.get("error_count", 0) + 1
            
            return state
            
        except Exception as e:
            logger.error(f"降级意图处理也失败: {str(e)}")
            # 最后的保底处理
            state["response_content"] = "抱歉，我现在遇到了一些技术问题，请稍后再试。"
            state["response_type"] = "error"
            return state
