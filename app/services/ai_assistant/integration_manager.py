#!/usr/bin/env python3
"""
集成管理器 - 统一管理和调用高级功能模块

这个模块负责将integration/目录下的各种高级功能模块
集成到主要的对话流程中，提供统一的接口和管理。
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass

from app.core.unified_config import unified_settings

logger = logging.getLogger(__name__)

@dataclass
class IntegrationConfig:
    """集成配置"""
    enable_workflow_orchestrator: bool = True
    enable_parameter_collector: bool = True
    enable_advanced_cache: bool = True
    enable_performance_monitor: bool = True
    enable_error_handler: bool = True
    enable_streaming_processor: bool = False
    enable_websocket_optimizer: bool = False
    
    # 启用的意图类型
    workflow_orchestrator_intents: List[str] = None
    
    def __post_init__(self):
        if self.workflow_orchestrator_intents is None:
            self.workflow_orchestrator_intents = [
                "training_plan", "exercise_action", "fitness_advice", "diet_advice"
            ]

class IntegrationManager:
    """
    集成管理器
    
    负责管理和协调各种高级功能模块的集成
    """
    
    def __init__(self, config: Optional[IntegrationConfig] = None):
        """
        初始化集成管理器
        
        Args:
            config: 集成配置，如果为None则使用默认配置
        """
        self.config = config or IntegrationConfig()
        self.initialized = False
        
        # 模块实例
        self.workflow_orchestrator = None
        self.parameter_collector = None
        self.advanced_cache = None
        self.performance_monitor = None
        self.error_handler = None
        self.streaming_processor = None
        self.websocket_optimizer = None
        
        logger.info("集成管理器已创建，等待初始化")
    
    async def initialize(self) -> bool:
        """
        初始化所有启用的模块
        
        Returns:
            是否成功初始化
        """
        if self.initialized:
            logger.info("集成管理器已经初始化过")
            return True
        
        logger.info("开始初始化集成管理器...")
        success_count = 0
        total_count = 0
        
        # 1. 初始化工作流编排器
        if self.config.enable_workflow_orchestrator:
            total_count += 1
            if await self._initialize_workflow_orchestrator():
                success_count += 1
        
        # 2. 初始化参数收集器
        if self.config.enable_parameter_collector:
            total_count += 1
            if await self._initialize_parameter_collector():
                success_count += 1
        
        # 3. 初始化高级缓存
        if self.config.enable_advanced_cache:
            total_count += 1
            if await self._initialize_advanced_cache():
                success_count += 1
        
        # 4. 初始化性能监控
        if self.config.enable_performance_monitor:
            total_count += 1
            if await self._initialize_performance_monitor():
                success_count += 1
        
        # 5. 初始化错误处理器
        if self.config.enable_error_handler:
            total_count += 1
            if await self._initialize_error_handler():
                success_count += 1
        
        # 6. 初始化流式处理器（可选）
        if self.config.enable_streaming_processor:
            total_count += 1
            if await self._initialize_streaming_processor():
                success_count += 1
        
        # 7. 初始化WebSocket优化器（可选）
        if self.config.enable_websocket_optimizer:
            total_count += 1
            if await self._initialize_websocket_optimizer():
                success_count += 1
        
        self.initialized = success_count > 0
        
        if self.initialized:
            logger.info(f"集成管理器初始化完成: {success_count}/{total_count} 个模块成功加载")
        else:
            logger.error("集成管理器初始化失败: 没有任何模块成功加载")
        
        return self.initialized
    
    async def _initialize_workflow_orchestrator(self) -> bool:
        """初始化工作流编排器"""
        try:
            from app.services.ai_assistant.integration.workflow_orchestrator import WorkflowOrchestrator
            
            # 使用简化的初始化参数，避免依赖问题
            self.workflow_orchestrator = WorkflowOrchestrator(
                db_session=None,  # 暂时不需要数据库
                llm_service=None  # 暂时不需要LLM服务
            )
            
            # 如果需要异步初始化
            if hasattr(self.workflow_orchestrator, 'initialize'):
                await self.workflow_orchestrator.initialize()
            
            logger.info("✅ 工作流编排器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 工作流编排器初始化失败: {str(e)}")
            # 创建简化的工作流编排器
            self.workflow_orchestrator = self._create_simple_workflow_orchestrator()
            logger.info("✅ 简化工作流编排器初始化成功")
            return True
    
    def _create_simple_workflow_orchestrator(self):
        """创建简化的工作流编排器"""
        class SimpleWorkflowOrchestrator:
            def __init__(self):
                self.initialized = True
            
            async def execute_workflow(self, **kwargs):
                """简化的工作流执行"""
                message = kwargs.get("message", "")
                intent = kwargs.get("intent", "general_chat")
                user_profile = kwargs.get("user_profile", {})
                
                # 基于意图生成响应
                if intent == "exercise_action":
                    response_content = f"基于您的问题「{message}」，我来为您推荐一些有效的健身动作和技巧。"
                elif intent == "training_plan":
                    response_content = f"我会为您制定个性化的训练计划。基于您的问题「{message}」，让我为您设计合适的训练方案。"
                elif intent == "fitness_advice":
                    response_content = f"关于您的健身问题「{message}」，我来给您一些专业的建议和指导。"
                elif intent == "diet_advice":
                    response_content = f"关于饮食问题「{message}」，我会为您提供营养建议和饮食指导。"
                else:
                    response_content = f"我来帮您解答关于「{message}」的问题。"
                
                return {
                    "success": True,
                    "response_content": response_content,
                    "workflow_metadata": {
                        "workflow_type": "simple",
                        "intent": intent,
                        "processing_system": "simple_workflow_orchestrator"
                    }
                }
        
        return SimpleWorkflowOrchestrator()
    
    async def _initialize_parameter_collector(self) -> bool:
        """初始化参数收集器"""
        try:
            from app.services.ai_assistant.integration.progressive_parameter_collector import ProgressiveParameterCollector
            self.parameter_collector = ProgressiveParameterCollector()
            
            if hasattr(self.parameter_collector, 'initialize'):
                await self.parameter_collector.initialize()
            
            logger.info("✅ 渐进式参数收集器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 参数收集器初始化失败: {str(e)}")
            return False
    
    async def _initialize_advanced_cache(self) -> bool:
        """初始化高级缓存"""
        try:
            from app.services.ai_assistant.integration.advanced_cache_optimizer import AdvancedCacheOptimizer
            self.advanced_cache = AdvancedCacheOptimizer()
            
            if hasattr(self.advanced_cache, 'initialize'):
                await self.advanced_cache.initialize()
            
            logger.info("✅ 高级缓存优化器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 高级缓存初始化失败: {str(e)}")
            return False
    
    async def _initialize_performance_monitor(self) -> bool:
        """初始化性能监控"""
        try:
            from app.services.ai_assistant.integration.performance_monitor import PerformanceMonitor
            self.performance_monitor = PerformanceMonitor()
            
            if hasattr(self.performance_monitor, 'initialize'):
                await self.performance_monitor.initialize()
            
            logger.info("✅ 性能监控器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 性能监控初始化失败: {str(e)}")
            return False
    
    async def _initialize_error_handler(self) -> bool:
        """初始化错误处理器"""
        try:
            from app.services.ai_assistant.integration.error_handler import ErrorHandler
            self.error_handler = ErrorHandler()
            
            if hasattr(self.error_handler, 'initialize'):
                await self.error_handler.initialize()
            
            logger.info("✅ 统一错误处理器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 错误处理器初始化失败: {str(e)}")
            return False
    
    async def _initialize_streaming_processor(self) -> bool:
        """初始化流式处理器"""
        try:
            from app.services.ai_assistant.integration.streaming_processor import StreamingProcessor
            self.streaming_processor = StreamingProcessor()
            
            if hasattr(self.streaming_processor, 'initialize'):
                await self.streaming_processor.initialize()
            
            logger.info("✅ 流式处理器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ 流式处理器初始化失败: {str(e)}")
            return False
    
    async def _initialize_websocket_optimizer(self) -> bool:
        """初始化WebSocket优化器"""
        try:
            from app.services.ai_assistant.integration.websocket_optimizer import WebSocketOptimizer
            self.websocket_optimizer = WebSocketOptimizer()
            
            if hasattr(self.websocket_optimizer, 'initialize'):
                await self.websocket_optimizer.initialize()
            
            logger.info("✅ WebSocket优化器初始化成功")
            return True
        except Exception as e:
            logger.error(f"❌ WebSocket优化器初始化失败: {str(e)}")
            return False
    
    def should_use_workflow_orchestrator(self, intent: str) -> bool:
        """
        检查是否应该使用工作流编排器处理该意图
        
        Args:
            intent: 意图类型
            
        Returns:
            是否使用工作流编排器
        """
        return (self.workflow_orchestrator is not None and 
                intent in self.config.workflow_orchestrator_intents)
    
    async def process_with_workflow(
        self, 
        message: str, 
        conversation_id: str, 
        user_info: Optional[Dict[str, Any]] = None,
        intent: str = "general_chat",
        confidence: float = 0.8
    ) -> Dict[str, Any]:
        """
        使用工作流编排器处理消息
        
        Args:
            message: 用户消息
            conversation_id: 会话ID
            user_info: 用户信息
            intent: 意图类型
            confidence: 置信度
            
        Returns:
            处理结果
        """
        if not self.workflow_orchestrator:
            raise RuntimeError("工作流编排器未初始化")
        
        # 使用性能监控（如果启用）
        if self.performance_monitor:
            async with self.performance_monitor.track_operation("workflow_process"):
                return await self._execute_workflow(message, conversation_id, user_info, intent, confidence)
        else:
            return await self._execute_workflow(message, conversation_id, user_info, intent, confidence)
    
    async def _execute_workflow(
        self, 
        message: str, 
        conversation_id: str, 
        user_info: Optional[Dict[str, Any]], 
        intent: str, 
        confidence: float
    ) -> Dict[str, Any]:
        """执行工作流处理"""
        try:
            # 准备工作流参数
            workflow_params = {
                "message": message,
                "conversation_id": conversation_id,
                "user_id": user_info.get("user_id") if user_info else conversation_id,
                "intent": intent,
                "confidence": confidence,
                "user_profile": user_info or {},
                "stream": False
            }
            
            # 执行工作流
            workflow_result = await self.workflow_orchestrator.execute_workflow(**workflow_params)
            
            # 转换工作流结果为标准响应格式
            response = self._convert_workflow_result(workflow_result, intent, confidence)
            
            # 记录性能指标（如果启用）
            if self.performance_monitor:
                await self.performance_monitor.record_metrics({
                    "intent": intent,
                    "workflow_success": response.get("success", True),
                    "processing_system": "workflow_orchestrator",
                    "response_length": len(response.get("response_content", ""))
                })
            
            return response
            
        except Exception as e:
            # 使用错误处理器（如果启用）
            if self.error_handler:
                handled_error = await self.error_handler.handle_error(e, {
                    "message": message,
                    "conversation_id": conversation_id,
                    "intent": intent,
                    "system": "workflow_orchestrator"
                })
                return handled_error
            else:
                logger.error(f"工作流处理失败: {str(e)}")
                return {
                    "response_content": f"抱歉，处理您的请求时出现了错误: {str(e)}",
                    "response_type": "error",
                    "intent": intent,
                    "confidence": 0.0,
                    "source_system": "workflow_orchestrator_error",
                    "error": str(e)
                }
    
    def _convert_workflow_result(
        self, 
        workflow_result: Dict[str, Any], 
        intent: str, 
        confidence: float
    ) -> Dict[str, Any]:
        """
        将工作流结果转换为标准响应格式
        
        Args:
            workflow_result: 工作流执行结果
            intent: 意图类型
            confidence: 置信度
            
        Returns:
            标准响应格式
        """
        response = {
            "response_content": workflow_result.get("response_content", ""),
            "response_type": "text",
            "intent": intent,
            "confidence": confidence,
            "source_system": "workflow_orchestrator",
            "success": workflow_result.get("success", True)
        }
        
        # 添加工作流特定的元数据
        if "workflow_metadata" in workflow_result:
            response["workflow_metadata"] = workflow_result["workflow_metadata"]
        
        # 添加结构化数据（如果有）
        if "structured_data" in workflow_result:
            response["structured_data"] = workflow_result["structured_data"]
        
        # 添加错误信息（如果有）
        if "error" in workflow_result:
            response["error"] = workflow_result["error"]
        
        return response
    
    def get_cache_service(self):
        """
        获取缓存服务
        
        Returns:
            高级缓存服务（如果启用）或None
        """
        return self.advanced_cache
    
    def get_performance_monitor(self):
        """
        获取性能监控器
        
        Returns:
            性能监控器（如果启用）或None
        """
        return self.performance_monitor
    
    def get_error_handler(self):
        """
        获取错误处理器
        
        Returns:
            错误处理器（如果启用）或None
        """
        return self.error_handler

# 全局集成管理器实例
_global_integration_manager: Optional[IntegrationManager] = None

async def get_integration_manager() -> IntegrationManager:
    """
    获取全局集成管理器实例
    
    Returns:
        集成管理器实例
    """
    global _global_integration_manager
    
    if _global_integration_manager is None:
        # 从配置确定启用的模块
        config = IntegrationConfig(
            enable_workflow_orchestrator=getattr(unified_settings, 'ENABLE_WORKFLOW_ORCHESTRATOR', True),
            enable_parameter_collector=getattr(unified_settings, 'ENABLE_PARAMETER_COLLECTOR', True),
            enable_advanced_cache=getattr(unified_settings, 'ENABLE_ADVANCED_CACHE', True),
            enable_performance_monitor=getattr(unified_settings, 'ENABLE_PERFORMANCE_MONITOR', True),
            enable_error_handler=getattr(unified_settings, 'ENABLE_ERROR_HANDLER', True),
            enable_streaming_processor=getattr(unified_settings, 'ENABLE_STREAMING_PROCESSOR', False),
            enable_websocket_optimizer=getattr(unified_settings, 'ENABLE_WEBSOCKET_OPTIMIZER', False)
        )
        
        _global_integration_manager = IntegrationManager(config)
        
        # 异步初始化
        await _global_integration_manager.initialize()
    
    return _global_integration_manager

def reset_integration_manager():
    """重置全局集成管理器（主要用于测试）"""
    global _global_integration_manager
    _global_integration_manager = None 