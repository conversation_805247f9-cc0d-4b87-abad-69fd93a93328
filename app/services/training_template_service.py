"""
训练模板服务类
提供训练模板相关的业务逻辑处理，包括数据查询、验证、转换等功能
"""

from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
import logging
from datetime import datetime

from app.models.training_template import WorkoutTemplate
from app.models.workout_exercise import WorkoutExercise
from app.models.exercise import Exercise, ExerciseDetail
from app.models.set_record import SetRecord
from app.models.user import User
from app.schemas.training_plan import TrainingTemplateCreate
from app.services.exercise_data_processor import ExerciseDataProcessor
from app.services.set_record_manager import SetRecordManager

logger = logging.getLogger(__name__)


class TrainingTemplateService:
    """训练模板服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.exercise_processor = ExerciseDataProcessor(db)
        self.set_record_manager = SetRecordManager(db)

    def get_template_with_details(self, template_id: int, user_id: int) -> Optional[WorkoutTemplate]:
        """
        获取包含完整详细信息的训练模板

        Args:
            template_id: 模板ID
            user_id: 用户ID

        Returns:
            训练模板对象，包含完整的关联数据
        """
        try:
            template = self.db.query(WorkoutTemplate).options(
                joinedload(WorkoutTemplate.template_exercises)
                .joinedload(WorkoutExercise.exercise)
                .joinedload(Exercise.details),
                joinedload(WorkoutTemplate.template_exercises)
                .joinedload(WorkoutExercise.set_records)
            ).filter(
                WorkoutTemplate.id == template_id,
                WorkoutTemplate.user_id == user_id
            ).first()

            return template
        except SQLAlchemyError as e:
            logger.error(f"获取训练模板失败: {str(e)}")
            return None

    def get_user_templates_with_details(self, user_id: int) -> List[WorkoutTemplate]:
        """
        获取用户的所有训练模板，包含完整详细信息

        Args:
            user_id: 用户ID

        Returns:
            训练模板列表
        """
        try:
            templates = self.db.query(WorkoutTemplate).options(
                joinedload(WorkoutTemplate.template_exercises)
                .joinedload(WorkoutExercise.exercise)
                .joinedload(Exercise.details),
                joinedload(WorkoutTemplate.template_exercises)
                .joinedload(WorkoutExercise.set_records)
            ).filter(
                WorkoutTemplate.user_id == user_id
            ).order_by(WorkoutTemplate.created_at.desc()).all()

            return templates
        except SQLAlchemyError as e:
            logger.error(f"获取用户训练模板失败: {str(e)}")
            return []

    def create_template(self, template_data: TrainingTemplateCreate, user_id: int) -> Optional[WorkoutTemplate]:
        """
        创建新的训练模板

        Args:
            template_data: 模板数据
            user_id: 用户ID

        Returns:
            创建的模板对象
        """
        try:
            # 创建新的训练模板
            new_template = WorkoutTemplate(
                user_id=user_id,
                name=template_data.name,
                description=template_data.description,
                estimated_duration=template_data.estimated_duration,
                target_body_parts=template_data.target_body_parts,
                training_scenario=template_data.training_scenario,
                visibility=template_data.visibility,
                notes=template_data.notes
            )

            self.db.add(new_template)
            self.db.flush()  # 获取模板ID

            # 创建关联的训练动作
            for idx, exercise_data in enumerate(template_data.exercises):
                # 处理exercise_id
                exercise_id = self.exercise_processor.resolve_exercise_id(exercise_data.exercise_id)

                workout_exercise = WorkoutExercise(
                    template_id=new_template.id,
                    exercise_id=exercise_id,
                    sets=exercise_data.sets,
                    reps=exercise_data.reps,
                    weight=exercise_data.weight,
                    rest_seconds=exercise_data.rest_seconds,
                    order=idx + 1,
                    notes=exercise_data.notes,
                    exercise_type=exercise_data.exercise_type,
                    superset_group=exercise_data.superset_group
                )
                self.db.add(workout_exercise)

            self.db.commit()

            # 重新查询以获取完整的关联数据
            return self.get_template_with_details(new_template.id, user_id)

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"创建训练模板失败: {str(e)}")
            return None

    def update_template(self, template_id: int, template_data: TrainingTemplateCreate, user_id: int) -> Optional[Dict[str, Any]]:
        """
        更新训练模板

        Args:
            template_id: 模板ID
            template_data: 更新数据
            user_id: 用户ID

        Returns:
            更新结果字典
        """
        try:
            # 获取现有模板
            existing_template = self.get_template_with_details(template_id, user_id)
            if not existing_template:
                return None

            # 更新基本字段
            template_fields_updated = self._update_template_fields(existing_template, template_data)

            # 更新训练动作
            exercises_updated = self._update_template_exercises(existing_template, template_data.exercises)

            # 提交更改
            if template_fields_updated or exercises_updated:
                self.db.commit()
                self.db.refresh(existing_template)

                return {
                    "message": "训练模板更新成功",
                    "updated_fields": template_fields_updated,
                    "exercises_updated": exercises_updated,
                    "template": existing_template.to_dict()
                }
            else:
                return {
                    "message": "训练模板无变更",
                    "updated_fields": [],
                    "exercises_updated": False,
                    "template": existing_template.to_dict()
                }

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"更新训练模板失败: {str(e)}")
            return None

    def _update_template_fields(self, template: WorkoutTemplate, template_data: TrainingTemplateCreate) -> List[str]:
        """更新模板基本字段"""
        updated_fields = []

        field_mappings = {
            'name': template_data.name,
            'description': template_data.description,
            'estimated_duration': template_data.estimated_duration,
            'target_body_parts': template_data.target_body_parts,
            'training_scenario': template_data.training_scenario,
            'visibility': template_data.visibility,
            'notes': template_data.notes
        }

        for field, new_value in field_mappings.items():
            current_value = getattr(template, field)
            if new_value != current_value:
                setattr(template, field, new_value)
                updated_fields.append(field)

        return updated_fields

    def _update_template_exercises(self, template: WorkoutTemplate, exercises_data: List) -> bool:
        """更新模板训练动作"""
        exercises_updated = False

        # 获取现有的训练动作
        existing_exercises = {ex.order: ex for ex in template.template_exercises}
        new_exercises_data = {idx + 1: ex for idx, ex in enumerate(exercises_data)}

        # 更新或创建训练动作
        for order, exercise_data in new_exercises_data.items():
            exercise_id = self.exercise_processor.resolve_exercise_id(exercise_data.exercise_id)

            if order in existing_exercises:
                # 更新现有动作
                if self._update_existing_exercise(existing_exercises[order], exercise_data, exercise_id):
                    exercises_updated = True
                # 更新组记录
                if self._update_exercise_set_records(existing_exercises[order], exercise_data):
                    exercises_updated = True
            else:
                # 创建新动作
                new_exercise = self._create_new_exercise(template.id, exercise_data, exercise_id, order)
                # 为新动作创建组记录
                if new_exercise and exercise_data.set_records:
                    self._create_exercise_set_records(new_exercise, exercise_data)
                exercises_updated = True

        # 删除不再需要的动作
        for order, existing_exercise in existing_exercises.items():
            if order not in new_exercises_data:
                self.db.delete(existing_exercise)
                exercises_updated = True

        return exercises_updated

    def _update_existing_exercise(self, existing_exercise: WorkoutExercise, exercise_data, exercise_id: int) -> bool:
        """更新现有训练动作"""
        updated = False

        field_mappings = {
            'exercise_id': exercise_id,
            'sets': exercise_data.sets,
            'reps': exercise_data.reps,
            'weight': exercise_data.weight,
            'rest_seconds': exercise_data.rest_seconds,
            'notes': exercise_data.notes,
            'exercise_type': exercise_data.exercise_type,
            'superset_group': exercise_data.superset_group
        }

        for field, new_value in field_mappings.items():
            current_value = getattr(existing_exercise, field)
            if new_value != current_value:
                setattr(existing_exercise, field, new_value)
                updated = True

        return updated

    def _create_new_exercise(self, template_id: int, exercise_data, exercise_id: int, order: int) -> WorkoutExercise:
        """创建新的训练动作"""
        new_workout_exercise = WorkoutExercise(
            template_id=template_id,
            exercise_id=exercise_id,
            sets=exercise_data.sets,
            reps=exercise_data.reps,
            weight=exercise_data.weight,
            rest_seconds=exercise_data.rest_seconds,
            order=order,
            notes=exercise_data.notes,
            exercise_type=exercise_data.exercise_type,
            superset_group=exercise_data.superset_group
        )
        self.db.add(new_workout_exercise)
        self.db.flush()  # 获取ID
        return new_workout_exercise

    def _update_exercise_set_records(self, workout_exercise: WorkoutExercise, exercise_data) -> bool:
        """更新训练动作的组记录"""
        if not exercise_data.set_records:
            return False

        updated = False

        # 获取现有的组记录
        existing_set_records = {sr.set_number: sr for sr in workout_exercise.set_records}

        # 处理新的组记录数据
        for set_record_data in exercise_data.set_records:
            set_number = set_record_data.set_number

            if set_number in existing_set_records:
                # 更新现有组记录
                existing_record = existing_set_records[set_number]
                if self._update_single_set_record(existing_record, set_record_data):
                    updated = True
            else:
                # 创建新的组记录
                self._create_single_set_record(workout_exercise.id, set_record_data)
                updated = True

        # 删除不再需要的组记录
        new_set_numbers = {sr.set_number for sr in exercise_data.set_records}
        for set_number, existing_record in existing_set_records.items():
            if set_number not in new_set_numbers:
                self.db.delete(existing_record)
                updated = True

        return updated

    def _create_exercise_set_records(self, workout_exercise: WorkoutExercise, exercise_data):
        """为新创建的训练动作创建组记录"""
        if not exercise_data.set_records:
            return

        for set_record_data in exercise_data.set_records:
            self._create_single_set_record(workout_exercise.id, set_record_data)

    def _update_single_set_record(self, existing_record, set_record_data) -> bool:
        """更新单个组记录"""
        updated = False

        field_mappings = {
            'set_type': set_record_data.set_type,
            'weight': set_record_data.weight,
            'reps': set_record_data.reps,
            'completed': set_record_data.completed,
            'notes': set_record_data.notes
        }

        for field, new_value in field_mappings.items():
            current_value = getattr(existing_record, field)
            if new_value != current_value:
                setattr(existing_record, field, new_value)
                updated = True

        return updated

    def _create_single_set_record(self, workout_exercise_id: int, set_record_data):
        """创建单个组记录"""
        # 使用SetRecordManager创建组记录
        set_data = {
            "set_number": set_record_data.set_number,
            "set_type": set_record_data.set_type,
            "weight": set_record_data.weight,
            "reps": set_record_data.reps,
            "completed": set_record_data.completed,
            "notes": set_record_data.notes
        }

        self.set_record_manager.create_set_record(workout_exercise_id, set_data)

    def apply_workout_template(self, template_id: int, user_id: int, scheduled_date: datetime, 
                             training_plan_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        应用训练模板创建新的训练日

        Args:
            template_id: 模板ID
            user_id: 用户ID
            scheduled_date: 训练计划日期
            training_plan_id: 训练计划ID（可选）

        Returns:
            创建的训练日信息字典
        """
        from app.models.workout import Workout
        
        try:
            # 获取模板详细信息
            template = self.get_template_with_details(template_id, user_id)
            if not template:
                logger.error(f"模板不存在或无权访问: template_id={template_id}, user_id={user_id}")
                return None

            if not template.template_exercises or len(template.template_exercises) == 0:
                logger.error(f"模板中没有训练动作: template_id={template_id}")
                return None

            # 创建Workout记录
            new_workout = Workout(
                training_plan_id=training_plan_id,
                user_id=user_id,  # 为独立训练日设置用户ID
                name=f"{template.name} - {scheduled_date.strftime('%Y-%m-%d')}",
                day_number=1,  # 默认值，如果属于训练计划可以后续调整
                description=template.description,
                estimated_duration=template.estimated_duration,
                target_body_parts=template.target_body_parts,
                training_scenario=template.training_scenario,
                scheduled_date=scheduled_date,
                status="not_started",
                # 设置模板来源信息
                template_source={
                    "template_id": template.id,
                    "template_name": template.name,
                    "applied_at": datetime.utcnow().isoformat(),
                    "template_description": template.description,
                    "template_estimated_duration": template.estimated_duration,
                    "template_target_body_parts": template.target_body_parts,
                    "template_training_scenario": template.training_scenario
                }
            )

            self.db.add(new_workout)
            self.db.flush()  # 获取workout ID

            # 创建训练动作记录
            created_exercises = []
            for template_exercise in template.template_exercises:
                # 创建WorkoutExercise记录
                new_workout_exercise = WorkoutExercise(
                    workout_id=new_workout.id,
                    exercise_id=template_exercise.exercise_id,
                    sets=template_exercise.sets,
                    reps=template_exercise.reps,
                    weight=template_exercise.weight,
                    rest_seconds=template_exercise.rest_seconds,
                    order=template_exercise.order,
                    notes=template_exercise.notes,
                    exercise_type=template_exercise.exercise_type,
                    superset_group=template_exercise.superset_group
                )
                self.db.add(new_workout_exercise)
                self.db.flush()  # 获取workout_exercise ID

                # 创建SetRecord记录
                # 如果模板中有组记录，复制这些记录
                if template_exercise.set_records:
                    for template_set_record in template_exercise.set_records:
                        new_set_record = SetRecord(
                            workout_exercise_id=new_workout_exercise.id,
                            set_number=template_set_record.set_number,
                            set_type=template_set_record.set_type,
                            weight=template_set_record.weight,
                            reps=template_set_record.reps,
                            completed=False,  # 新创建的记录默认未完成
                            notes=template_set_record.notes
                        )
                        self.db.add(new_set_record)
                else:
                    # 如果模板中没有组记录，根据sets数量创建默认记录
                    for set_num in range(1, template_exercise.sets + 1):
                        new_set_record = SetRecord(
                            workout_exercise_id=new_workout_exercise.id,
                            set_number=set_num,
                            set_type="normal",
                            weight=float(template_exercise.weight) if template_exercise.weight and template_exercise.weight.replace('.', '').isdigit() else None,
                            reps=int(template_exercise.reps) if template_exercise.reps and template_exercise.reps.isdigit() else None,
                            completed=False,
                            notes=None
                        )
                        self.db.add(new_set_record)

                created_exercises.append(new_workout_exercise)

            self.db.commit()

            # 返回创建的训练日信息
            self.db.refresh(new_workout)
            return {
                "workout_id": new_workout.id,
                "workout": {
                    "id": new_workout.id,
                    "training_plan_id": new_workout.training_plan_id,
                    "user_id": new_workout.user_id,
                    "name": new_workout.name,
                    "day_number": new_workout.day_number,
                    "day_of_week": new_workout.day_of_week,
                    "description": new_workout.description,
                    "scheduled_date": new_workout.scheduled_date.isoformat() if new_workout.scheduled_date else None,
                    "estimated_duration": new_workout.estimated_duration,
                    "target_body_parts": new_workout.target_body_parts,
                    "training_scenario": new_workout.training_scenario,
                    "status": new_workout.status,
                    "actual_duration": new_workout.actual_duration,
                    "net_duration": new_workout.net_duration,
                    "start_time": new_workout.start_time.isoformat() if new_workout.start_time else None,
                    "end_time": new_workout.end_time.isoformat() if new_workout.end_time else None,
                    "last_status_change": new_workout.last_status_change.isoformat() if new_workout.last_status_change else None,
                    "pause_records": new_workout.pause_records if new_workout.pause_records else [],
                    "status_history": new_workout.status_history if new_workout.status_history else [],
                    "template_source": new_workout.template_source,
                    "created_at": new_workout.created_at.isoformat() if new_workout.created_at else None,
                    "updated_at": new_workout.updated_at.isoformat() if new_workout.updated_at else None
                },
                "exercises": [
                    {
                        "id": ex.id,
                        "exercise_id": ex.exercise_id,
                        "exercise_name": ex.exercise.name if ex.exercise else None,
                        "sets": ex.sets,
                        "reps": ex.reps,
                        "weight": ex.weight,
                        "rest_seconds": ex.rest_seconds,
                        "order": ex.order,
                        "notes": ex.notes,
                        "exercise_type": ex.exercise_type,
                        "superset_group": ex.superset_group
                    }
                    for ex in created_exercises
                ]
            }

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"应用训练模板失败: {str(e)}")
            return None
