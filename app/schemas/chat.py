from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field
from datetime import datetime
from app.models.message import MessageRole

class ChatMessage(BaseModel):
    role: str  # "user", "assistant", "system"
    content: str

class IntentData(BaseModel):
    """意图数据模型"""
    intent: str = Field(..., description="识别出的用户意图类型")
    confidence: float = Field(..., description="置信度，范围0-1")
    parameters: Optional[Dict[str, Any]] = Field(None, description="意图参数，例如目标身体部位等")


class InfoStatus(BaseModel):
    """用户信息状态模型"""
    missing_fields: List[str] = Field([], description="缺失的用户信息字段")
    complete: bool = Field(True, description="用户信息是否完整")
    completion_percentage: float = Field(100.0, description="用户信息完整度百分比")


class MetaInfo(BaseModel):
    """消息元数据模型"""
    intent: Optional[str] = None
    confidence: Optional[float] = None
    parameters: Optional[Dict[str, Any]] = None
    info_status: Optional[InfoStatus] = None
    waiting_for_info: Optional[List[str]] = None


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str
    session_id: Optional[str] = None
    system_prompt: Optional[str] = None
    quick_intent: Optional[str] = Field(None, description="快速意图，直接进入特定对话流程")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    response: str
    session_id: str
    meta_info: Optional[Dict[str, Any]] = Field(None, description="消息元数据，包含意图识别结果等")


class UserInfoUpdateRequest(BaseModel):
    """用户信息更新请求模型"""
    session_id: str = Field(..., description="会话ID")
    field: str = Field(..., description="要更新的字段名")
    value: Any = Field(..., description="字段值")
    value_text: str = Field(..., description="用户输入的原始文本，用于保存消息记录")


# 会话相关模型
class ConversationBase(BaseModel):
    """会话基础模型"""
    session_id: str
    is_active: bool = True
    meta_info: Optional[Dict[str, Any]] = None


class ConversationCreate(ConversationBase):
    """会话创建模型"""
    pass


class ConversationUpdate(BaseModel):
    """会话更新模型"""
    is_active: Optional[bool] = None
    meta_info: Optional[Dict[str, Any]] = None


class ConversationInDBBase(ConversationBase):
    """会话数据库模型基类"""
    id: int
    user_id: int
    start_time: datetime
    last_active: datetime
    
    class Config:
        from_attributes = True  # 替换了 orm_mode = True


class Conversation(ConversationInDBBase):
    """会话返回模型"""
    pass


class ConversationList(BaseModel):
    """会话列表模型"""
    conversations: List[Conversation]
    total: int


# 消息相关模型
class MessageBase(BaseModel):
    """消息基础模型"""
    content: str
    role: MessageRole
    meta_info: Optional[Dict[str, Any]] = None


class MessageCreate(MessageBase):
    """消息创建模型"""
    pass


class MessageUpdate(BaseModel):
    """消息更新模型"""
    content: Optional[str] = None
    role: Optional[MessageRole] = None
    meta_info: Optional[Dict[str, Any]] = None


class MessageInDBBase(MessageBase):
    """消息数据库模型基类"""
    id: int
    conversation_id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True  # 替换了 orm_mode = True


class Message(MessageInDBBase):
    """消息返回模型"""
    pass


class MessageList(BaseModel):
    """消息列表模型"""
    messages: List[Message]
    total: int
    from_cache: Optional[bool] = False


class RecentMessageList(BaseModel):
    """最近消息列表模型"""
    messages: List[Message]
    conversations: Dict[str, Any]
    total: int


class BasicResponse(BaseModel):
    """基础响应模型"""
    status: str
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None 