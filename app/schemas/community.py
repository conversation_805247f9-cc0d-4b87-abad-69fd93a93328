from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from app.schemas.base import DateTimeModelMixin
from app.schemas.user import UserProfile
from app.models.community.notification import NotificationType

# WorkoutShare Schemas
class WorkoutExerciseSet(BaseModel):
    """训练动作单组数据模型"""
    id: Optional[int] = None
    workout_exercise_id: Optional[int] = None
    type: str = "normal"
    weight: float = 0
    reps: int = 0
    completed: bool = False
    notes: Optional[str] = None

class WorkoutExerciseItem(BaseModel):
    """训练动作数据模型"""
    id: Optional[int] = None
    name: str
    imageUrl: Optional[str] = None
    category: Optional[str] = None
    sets: List[WorkoutExerciseSet] = []
    hasCompletedSets: Optional[bool] = False
    isExpanded: Optional[bool] = False
    totalVolume: Optional[float] = 0
    completedSets: Optional[int] = 0

class WorkoutDataSchema(BaseModel):
    """训练数据模型"""
    exercises: List[WorkoutExerciseItem] = []
    duration_seconds: Optional[int] = 0
    total_sets: Optional[int] = 0
    total_volume: Optional[float] = 0

class WorkoutShareCreate(BaseModel):
    """用于从Workout创建DailyWorkout分享的模型"""
    title: Optional[str] = None
    content: Optional[str] = None
    visibility: str = "Everyone"
    images: Optional[List[str]] = []  # 添加图片URLs列表
    workout_data: Optional[WorkoutDataSchema] = None  # 训练数据
    tags: Optional[List[str]] = []  # 标签列表

# WorkoutData Schema
class WorkoutData(BaseModel):
    """训练数据模型，用于在创建帖子时同时创建DailyWorkout"""
    exercises: Optional[List] = []
    duration_seconds: Optional[int] = None
    total_sets: Optional[int] = None
    total_volume: Optional[str] = None

# DailyWorkout Schemas
class DailyWorkoutExerciseBase(BaseModel):
    """训练动作基础模型"""
    exercise_id: int
    sets: int
    reps: str
    rest_seconds: Optional[int] = 60
    order: int
    notes: Optional[str] = None
    exercise_type: str = "weight_reps"
    superset_group: Optional[int] = None
    weight: Optional[str] = None


class DailyWorkoutExerciseCreate(DailyWorkoutExerciseBase):
    """用于创建训练动作的模型"""
    pass


class DailyWorkoutExerciseInDB(DailyWorkoutExerciseBase):
    """数据库中的训练动作模型"""
    id: int
    daily_workout_id: int

    class Config:
        from_attributes = True


class DailyWorkoutExerciseWithDetail(DailyWorkoutExerciseInDB):
    """包含详细信息的训练动作模型"""
    exercise_name: str
    exercise_image: Optional[str] = None
    exercise_description: Optional[str] = None


class DailyWorkoutBase(BaseModel):
    """单日训练基础模型"""
    title: str = Field(..., min_length=1, max_length=100)
    content: str = Field(..., min_length=1)
    description: Optional[str] = None
    training_duration: Optional[int] = None  # 训练时长（分钟）
    total_capacity: Optional[int] = None  # 总容量


class DailyWorkoutCreate(DailyWorkoutBase):
    """用于创建单日训练的模型"""
    exercises: Optional[List[DailyWorkoutExerciseCreate]] = []


class DailyWorkoutUpdate(DailyWorkoutBase):
    """用于更新单日训练的模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=100)
    content: Optional[str] = Field(None, min_length=1)
    description: Optional[str] = None
    training_duration: Optional[int] = None
    total_capacity: Optional[int] = None


class DailyWorkoutInDB(DailyWorkoutBase, DateTimeModelMixin):
    """数据库中的单日训练模型"""
    id: int
    user_id: int
    status: str
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True,
        "extra": "ignore"
    }


class DailyWorkoutWithExercises(DailyWorkoutInDB):
    """包含训练动作的单日训练模型"""
    exercises: List[DailyWorkoutExerciseWithDetail] = []


# 定义 DailyWorkoutResponse 作为 DailyWorkoutWithExercises 的别名
DailyWorkoutResponse = DailyWorkoutWithExercises


# Post Schemas
class PostBase(BaseModel):
    """帖子基础模型"""
    title: str = Field(..., min_length=1, max_length=100)
    content: str = Field(..., min_length=1)
    image_urls: Optional[List[str]] = None
    related_workout_id: Optional[int] = None


class PostCreate(PostBase):
    """用于创建帖子的模型"""
    workout_data: Optional[WorkoutData] = None
    daily_workout_id: Optional[int] = None  # 添加DailyWorkout的ID
    images: Optional[List[str]] = []  # 添加图片URLs列表，用于替代废弃的image_urls
    visibility: str = "Everyone"
    tags: Optional[List[str]] = []  # 添加标签列表


class PostUpdate(PostBase):
    """用于更新帖子的模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=100)
    content: Optional[str] = Field(None, min_length=1)
    image_urls: Optional[List[str]] = None
    related_workout_id: Optional[int] = None
    visibility: Optional[str] = None


class PostInDB(PostBase, DateTimeModelMixin):
    """数据库中的帖子模型"""
    id: int
    user_id: int
    like_count: int = 0
    comment_count: int = 0
    share_count: int = 0
    status: str
    reported_count: int = 0
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True,
        "extra": "ignore"
    }


# 简化的训练模型，用于帖子中的关联训练
class SimpleWorkout(BaseModel):
    """简化的训练模型，用于帖子响应"""
    id: int
    name: Optional[str] = None
    title: Optional[str] = None
    date: Optional[str] = None  # 使用字符串格式的日期

    model_config = {
        "from_attributes": True,
        "extra": "ignore"
    }


class PostRead(PostInDB):
    """用于读取帖子的模型，包含额外信息"""
    user: UserProfile
    like_count: int = 0
    comment_count: int = 0
    is_liked_by_current_user: bool = False
    related_workout: Optional[SimpleWorkout] = None
    related_workout_detail: Optional[Dict[str, Any]] = None
    images: Optional[List[Dict[str, Any]]] = []
    comments_summary: Optional[List[Dict[str, Any]]] = []
    tags: Optional[List[str]] = []  # 添加标签列表
    view_count: int = 0

    model_config = {
        "from_attributes": True,
        "extra": "ignore"
    }


# 定义 PostResponse 作为 PostRead 的别名
PostResponse = PostRead


# Comment Schemas
class CommentBase(BaseModel):
    """评论基础模型"""
    content: str = Field(..., min_length=1)
    post_id: int
    parent_id: Optional[int] = None


class CommentCreate(CommentBase):
    """用于创建评论的模型"""
    pass


class CommentUpdate(BaseModel):
    """用于更新评论的模型"""
    content: Optional[str] = None


class CommentInDB(CommentBase, DateTimeModelMixin):
    """数据库中的评论模型"""
    id: int
    user_id: int
    post_id: int
    parent_id: Optional[int] = None
    status: str
    reported_count: int = 0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CommentRead(CommentInDB):
    """用于读取评论的模型，包含额外信息"""
    user: UserProfile
    like_count: int = 0
    is_liked_by_current_user: bool = False
    replies: Optional[List["CommentRead"]] = []


# 定义 CommentResponse 作为 CommentRead 的别名
CommentResponse = CommentRead


# Like Schemas
class LikeBase(BaseModel):
    """点赞基础模型"""
    pass


class PostLikeCreate(LikeBase):
    """用于创建帖子点赞的模型"""
    post_id: int


class CommentLikeCreate(LikeBase):
    """用于创建评论点赞的模型"""
    comment_id: int


class LikeInDB(LikeBase, DateTimeModelMixin):
    """数据库中的点赞模型"""
    id: int
    user_id: int

    class Config:
        from_attributes = True


class PostLikeInDB(LikeInDB):
    """数据库中的帖子点赞模型"""
    post_id: int


class CommentLikeInDB(LikeInDB):
    """数据库中的评论点赞模型"""
    comment_id: int


# Report Schemas
class ReportBase(BaseModel):
    """举报基础模型"""
    reason: str


class ReportCreate(ReportBase):
    """用于创建举报的模型"""
    post_id: Optional[int] = None
    comment_id: Optional[int] = None


class ReportInDB(ReportBase, DateTimeModelMixin):
    """数据库中的举报模型"""
    id: int
    reporter_id: int
    post_id: Optional[int] = None
    comment_id: Optional[int] = None
    status: str
    resolved_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ReportRead(ReportInDB):
    """用于读取举报的模型，包含额外信息"""
    reporter: UserProfile
    post: Optional[PostInDB] = None
    comment: Optional[CommentInDB] = None


# Notification Schemas
class NotificationBase(BaseModel):
    """通知基础模型"""
    type: NotificationType
    content: str
    is_read: bool = False


class NotificationCreate(NotificationBase):
    """用于创建通知的模型"""
    user_id: int
    related_post_id: Optional[int] = None
    related_comment_id: Optional[int] = None
    related_user_id: Optional[int] = None


class NotificationInDB(NotificationBase, DateTimeModelMixin):
    """数据库中的通知模型"""
    id: int
    user_id: int
    is_read: bool = False
    related_post_id: Optional[int] = None
    related_comment_id: Optional[int] = None
    related_user_id: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True


class NotificationRead(NotificationInDB):
    """用于读取通知的模型，包含额外信息"""
    related_post: Optional[PostInDB] = None
    related_comment: Optional[CommentInDB] = None
    related_user: Optional[UserProfile] = None


# 定义 NotificationResponse 作为 NotificationRead 的别名
NotificationResponse = NotificationRead


# Moderation Schemas
class ModerationAction(BaseModel):
    """审核操作模型"""
    status: str = Field(..., description="审核状态: approved, rejected")
    reason: Optional[str] = None


# List Response Schemas
class PostListResponse(BaseModel):
    """帖子列表响应模型"""
    total: int
    items: List[PostRead]

    model_config = {
        "from_attributes": True,
        "extra": "ignore"
    }


class CommentListResponse(BaseModel):
    """评论列表响应模型"""
    total: int
    items: List[CommentRead]

    class Config:
        from_attributes = True


class NotificationListResponse(BaseModel):
    """通知列表响应模型"""
    total: int
    items: List[NotificationRead]
    unread_count: int

    class Config:
        from_attributes = True


# User Relation Schemas
class UserRelationBase(BaseModel):
    follower_id: int
    following_id: int


class UserRelationResponse(UserRelationBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


class ImageBase(BaseModel):
    url: str
    title: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    post_id: Optional[int] = None


class ImageCreate(ImageBase):
    pass


class ImageUpdate(ImageBase):
    url: Optional[str] = None
    title: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    post_id: Optional[int] = None


class ImageResponse(ImageBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True