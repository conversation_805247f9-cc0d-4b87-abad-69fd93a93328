from typing import Optional
from pydantic import BaseModel
from datetime import datetime


# 共享属性
class UserSettingBase(BaseModel):
    notification_enabled: Optional[bool] = True
    ai_character_type: Optional[str] = "motivational"


# 创建时需要的属性
class UserSettingCreate(UserSettingBase):
    user_id: int


# 更新时可以修改的属性
class UserSettingUpdate(UserSettingBase):
    pass


# 数据库中完整的用户设置
class UserSettingInDBBase(UserSettingBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  # 替换了 from_attributes = True  # 替换了 from_attributes = True  # 替换了 orm_mode = Trueclass UserSetting(UserSettingInDBBase):
    pass


# 数据库中存储的完整用户设置
class UserSettingInDB(UserSettingInDBBase):
    pass
