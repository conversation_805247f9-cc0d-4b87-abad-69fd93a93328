from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.team.enums import TeamStatus

# 创建团队请求
class TeamCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    settings: Optional[Dict[str, Any]] = None

# 更新团队请求
class TeamUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    status: Optional[TeamStatus] = None
    settings: Optional[Dict[str, Any]] = None

# 团队基本响应
class TeamResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    owner_id: int
    status: TeamStatus
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True  # 替换了 orm_mode = True


class TeamListResponse(TeamResponse):
    member_count: int
    client_count: int
    role: str  # 当前用户在团队中的角色

    class Config:
        from_attributes = True  # 替换了 orm_mode = True


class TeamDetail(TeamResponse):
    settings: Dict[str, Any]
    stats: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True  # 替换了 orm_mode = True