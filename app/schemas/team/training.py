from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

# 创建会员训练计划请求
class ClientPlanCreate(BaseModel):
    training_plan_id: int
    start_date: datetime
    end_date: datetime
    scheduled_time: Dict[str, Any]  # 例如 {"1": "09:00", "3": "16:00", "5": "10:30"}
    notes: Optional[str] = None

# 更新会员训练计划请求
class ClientPlanUpdate(BaseModel):
    status: Optional[str] = None
    end_date: Optional[datetime] = None
    scheduled_time: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None

# 会员训练计划响应
class ClientTrainingPlanResponse(BaseModel):
    id: int
    client_relation_id: int
    training_plan_id: int
    coach_id: int
    status: str
    start_date: datetime
    end_date: datetime
    scheduled_time: Dict[str, Any]
    next_session: Optional[datetime] = None
    completion_rate: float
    last_workout_date: Optional[datetime] = None
    notes: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# 训练课程响应
class SessionResponse(BaseModel):
    id: int
    client_plan_id: int
    scheduled_start: datetime
    actual_start: Optional[datetime] = None
    actual_end: Optional[datetime] = None
    status: str
    completion_rate: float
    feedback: Optional[str] = None
    mood_rating: Optional[int] = None
    difficulty_rating: Optional[int] = None
    notes: Optional[str] = None
    
    class Config:
        from_attributes = True

# 创建组记录请求
class SetRecordCreate(BaseModel):
    weight: float
    reps: int
    rpe: Optional[int] = None
    notes: Optional[str] = None

# 训练课程反馈请求
class SessionFeedback(BaseModel):
    feedback: Optional[str] = None
    mood_rating: Optional[int] = Field(None, ge=1, le=5)
    difficulty_rating: Optional[int] = Field(None, ge=1, le=5)
    notes: Optional[str] = None
