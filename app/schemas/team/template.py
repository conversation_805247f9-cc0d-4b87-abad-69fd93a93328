from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

# 创建训练计划模板请求
class TemplateCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    duration_weeks: int = Field(..., ge=1, le=52)
    sessions_per_week: int = Field(..., ge=1, le=7)
    difficulty_level: Optional[int] = Field(None, ge=1, le=5)
    target_audience: Optional[str] = None
    equipment_required: Optional[List[int]] = None
    is_public: bool = False
    exercises: List[Dict[str, Any]]

# 训练计划模板响应
class TemplateResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    creator_id: int
    team_id: int
    duration_weeks: int
    sessions_per_week: int
    difficulty_level: Optional[int] = None
    target_audience: Optional[str] = None
    equipment_required: Optional[List[int]] = None
    is_public: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    creator_name: str
    
    class Config:
        from_attributes = True

# 模板列表响应
class TemplateListResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    creator_id: int
    team_id: int
    duration_weeks: int
    sessions_per_week: int
    difficulty_level: Optional[int] = None
    is_public: bool
    created_at: datetime
    creator_name: str
    
    class Config:
        from_attributes = True