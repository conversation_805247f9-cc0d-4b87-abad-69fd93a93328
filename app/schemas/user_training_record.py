from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime


class UserTrainingRecordBase(BaseModel):
    """用户训练记录基础模型"""
    date: datetime
    body_parts: Optional[List[str]] = None
    duration_minutes: Optional[int] = None
    exercises_data: Dict[str, Any]
    notes: Optional[str] = None


class UserTrainingRecordCreate(UserTrainingRecordBase):
    """用户训练记录创建模型"""
    pass


class UserTrainingRecordUpdate(BaseModel):
    """用户训练记录更新模型"""
    date: Optional[datetime] = None
    body_parts: Optional[List[str]] = None
    duration_minutes: Optional[int] = None
    exercises_data: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None


class UserTrainingRecordInDBBase(UserTrainingRecordBase):
    """用户训练记录数据库模型基类"""
    id: int
    user_id: int
    
    class Config:
        from_attributes = True  # 替换了 from_attributes = True  # 替换了 from_attributes = True  # 替换了 orm_mode = Trueclass UserTrainingRecord(UserTrainingRecordInDBBase):
    """用户训练记录返回模型"""
    pass


class UserTrainingRecordList(BaseModel):
    """用户训练记录列表模型"""
    records: List[UserTrainingRecord]
    total: int 