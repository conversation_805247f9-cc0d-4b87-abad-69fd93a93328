from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, field_validator

class WorkoutStatusEnum(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"

class PauseRecord(BaseModel):
    pause_start: datetime
    pause_end: Optional[datetime] = None
    duration: Optional[int] = None  # 分钟

class StatusRecord(BaseModel):
    status: WorkoutStatusEnum
    timestamp: datetime
    duration: Optional[int] = None  # 该状态持续的分钟数

class WorkoutBase(BaseModel):
    """训练日基础模型"""
    name: str
    day_of_week: Optional[int] = None
    day_number: int
    description: Optional[str] = None
    estimated_duration: Optional[int] = None  # 预计时长（分钟）
    scheduled_date: Optional[datetime] = None  # 计划执行日期
    status: WorkoutStatusEnum = WorkoutStatusEnum.NOT_STARTED

class WorkoutCreate(WorkoutBase):
    """用于创建训练日的模型"""
    training_plan_id: Optional[int] = None  # 修改为可选，支持独立训练日

class WorkoutUpdate(BaseModel):
    """用于更新训练日的模型"""
    name: Optional[str] = None
    day_of_week: Optional[int] = None
    description: Optional[str] = None
    estimated_duration: Optional[int] = None
    scheduled_date: Optional[datetime] = None
    status: Optional[WorkoutStatusEnum] = None

class WorkoutStatusUpdate(BaseModel):
    """训练状态更新模型"""
    status: WorkoutStatusEnum

class WorkoutInDB(WorkoutBase):
    """数据库中的训练日模型"""
    id: int
    training_plan_id: Optional[int] = None  # 修改为可选，支持独立训练日
    created_at: datetime
    updated_at: datetime
    actual_duration: Optional[int] = None
    net_duration: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    last_status_change: Optional[datetime] = None
    pause_records: List[Dict[str, Any]] = []
    status_history: List[Dict[str, Any]] = []
    training_session_id: Optional[int] = None

    class Config:
        from_attributes = True

class WorkoutWithExercises(WorkoutInDB):
    """包含训练动作的训练日模型"""
    exercises: List = []  # 这里应引用 WorkoutExerciseWithDetail，但避免循环导入

class WorkoutStatistics(BaseModel):
    """训练统计数据模型"""
    workout_id: int
    status: str
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    net_duration: Optional[int] = None
    total_pause_time: int = 0
    pause_count: int = 0
    time_efficiency: float = 0  # 净训练时间/总训练时间的百分比

# 新增的Schema类
class WorkoutExerciseUpdateOrder(BaseModel):
    """用于更新训练动作顺序的模型"""
    id: int
    order: int

class SetRecordUpdateItem(BaseModel):
    """用于更新组记录的模型"""
    id: Optional[int] = None  # 若有ID则更新，无则创建
    set_number: int
    set_type: str = "normal"
    weight: Optional[float] = None
    reps: Optional[int] = None
    completed: Optional[bool] = False
    notes: Optional[str] = None

class WorkoutExerciseFullUpdate(BaseModel):
    """用于全面更新训练动作的模型"""
    id: Optional[int] = None  # 若有ID则更新，无则创建
    exercise_id: Optional[int] = None
    sets: Optional[int] = None
    reps: Optional[str] = None
    rest_seconds: Optional[int] = None
    order: Optional[int] = None
    notes: Optional[str] = None
    exercise_type: Optional[str] = None
    superset_group: Optional[int] = None
    weight: Optional[str] = None
    set_records: Optional[List[SetRecordUpdateItem]] = None
    delete: Optional[bool] = False  # 若为True则删除此训练动作

class WorkoutFullUpdate(BaseModel):
    """用于全面更新训练日的模型，包括基本信息、训练动作和组记录"""
    # 基本信息更新
    name: Optional[str] = None
    day_of_week: Optional[int] = None
    description: Optional[str] = None
    estimated_duration: Optional[int] = None
    scheduled_date: Optional[datetime] = None
    status: Optional[WorkoutStatusEnum] = None
    
    # 训练动作更新
    exercises: Optional[List[WorkoutExerciseFullUpdate]] = None

class WorkoutResponse(BaseModel):
    id: int
    training_plan_id: Optional[int] = None
    user_id: Optional[int] = None  # 新增：用户ID字段
    name: str
    day_of_week: Optional[int] = None
    day_number: int = 1  # 设置默认值
    description: Optional[str] = None
    estimated_duration: Optional[int] = None
    target_body_parts: Optional[List[int]] = None
    training_scenario: Optional[str] = None
    scheduled_date: Optional[datetime] = None
    status: str = "not_started"
    actual_duration: Optional[int] = None
    net_duration: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    last_status_change: Optional[datetime] = None
    pause_records: Optional[List[Dict[str, Any]]] = []
    status_history: Optional[List[Dict[str, Any]]] = []
    template_source: Optional[Dict[str, Any]] = None  # 新增：模板来源信息
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ApplyTemplateRequest(BaseModel):
    """应用训练模板请求schema"""
    date: str = Field(..., description="训练日期，格式为YYYY-MM-DD")
    training_plan_id: Optional[int] = Field(None, description="训练计划ID（可选）")
    
    @field_validator('date')
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, "%Y-%m-%d")
            return v
        except ValueError:
            raise ValueError('日期格式无效，请使用YYYY-MM-DD格式')

class ApplyTemplateResponse(BaseModel):
    """应用训练模板响应schema"""
    message: str
    workout_id: int
    workout: WorkoutResponse
    exercises: List[Dict[str, Any]]
    applied_date: str

    class Config:
        from_attributes = True

# 新增：用于创建训练日的嵌套模型
class SetRecordCreateNested(BaseModel):
    """用于嵌套创建组记录的模型（忽略ID字段）"""
    set_number: int
    set_type: str = "normal"  # 组类型：warmup, normal, drop, superset, rest
    weight: Optional[float] = None
    reps: Optional[int] = None
    completed: bool = False
    notes: Optional[str] = None

class WorkoutExerciseCreateNested(BaseModel):
    """用于嵌套创建训练动作的模型（忽略ID字段）"""
    exercise_id: int  # 引用现有动作，必须提供
    sets: int = 3
    reps: str = "10"
    rest_seconds: Optional[int] = 60
    weight: Optional[str] = None
    order: int = 1
    notes: Optional[str] = None
    exercise_type: str = "weight_reps"
    superset_group: Optional[int] = None
    set_records: Optional[List[SetRecordCreateNested]] = []

class WorkoutCreateFull(BaseModel):
    """用于创建完整训练日的模型（包含嵌套的训练动作和组记录）"""
    # 基本信息
    name: str
    day_of_week: Optional[int] = None
    day_number: int = 1
    description: Optional[str] = None
    estimated_duration: Optional[int] = None  # 预计时长（分钟）
    scheduled_date: Optional[datetime] = None  # 计划执行日期
    status: WorkoutStatusEnum = WorkoutStatusEnum.NOT_STARTED
    training_plan_id: Optional[int] = None  # 可选，支持独立训练日
    target_body_parts: Optional[List[int]] = None
    training_scenario: Optional[str] = None

    # 嵌套的训练动作
    workout_exercises: List[WorkoutExerciseCreateNested] = []

class WorkoutExerciseResponse(BaseModel):
    """训练动作响应模型"""
    id: int
    workout_id: int
    exercise_id: int
    exercise_name: Optional[str] = None
    exercise_image: Optional[str] = None
    exercise_description: Optional[str] = None
    video_url: Optional[str] = None
    sets: int
    reps: str
    rest_seconds: Optional[int] = None
    weight: Optional[str] = None
    order: int
    notes: Optional[str] = None
    exercise_type: str
    superset_group: Optional[int] = None
    set_records: List[Dict[str, Any]] = []

    class Config:
        from_attributes = True

class WorkoutCreateResponse(BaseModel):
    """创建训练日的响应模型"""
    id: int
    training_plan_id: Optional[int] = None
    user_id: int
    name: str
    day_of_week: Optional[int] = None
    day_number: int
    description: Optional[str] = None
    estimated_duration: Optional[int] = None
    target_body_parts: Optional[List[int]] = None
    training_scenario: Optional[str] = None
    scheduled_date: Optional[datetime] = None
    status: str
    created_at: datetime
    updated_at: datetime
    workout_exercises: List[WorkoutExerciseResponse] = []

    class Config:
        from_attributes = True