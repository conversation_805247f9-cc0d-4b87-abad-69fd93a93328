from app.crud.base import CRUDBase
from app.crud.user import CRUDUser
from app.crud.user_setting import CRUDUserSetting
from app.crud.meal import CRUDMealRecord, CRUDFoodItem, CRUDFoodItemNutrientIntake, CRUDHealthRecommendation, CRUDFoodRecognition
from app.crud.food import CRUDFood
from app.crud.share_track import CRUDShareTrack
from app.crud.food_recognition import CRUDFoodRecognition
from app.crud.food_item_nutrient_intake import CRUDFoodItemNutrientIntake
from app.crud.health_recommendation import CRUDHealthRecommendation

# 导入LangChain相关CRUD
from app.crud.crud_conversation import CRUDConversation, crud_conversation
from app.crud.crud_message import CRUDMessage, crud_message
from app.crud.crud_qa_pair import CRUDQAPair, crud_qa_pair
from app.crud.crud_user_setting import crud_user_setting
# 导入 crud_user 函数
from app.crud.crud_user import update_user_info
# 修正导入，crud_nutrient中没有crud_vitamin_rni
# from app.crud.crud_nutrient import crud_vitamin_rni
from app.crud.crud_user_training_record import crud_user_training_record

# 导入训练计划相关CRUD
from app.crud.crud_training_plan import CRUDTrainingPlan, training_plan as crud_training_plan
from app.crud.crud_workout import CRUDWorkout, crud_workout
from app.crud.crud_workout_exercise import CRUDWorkoutExercise, workout_exercise as crud_workout_exercise
from app.crud.crud_set_record import CRUDSetRecord, set_record as crud_set_record

# 导入健身动作相关CRUD
from app.crud.crud_exercise import (
    CRUDExercise, exercise as crud_exercise,
    CRUDExerciseDetail, exercise_detail as crud_exercise_detail,
    CRUDMuscle, muscle as crud_muscle,
    CRUDBodyPart, body_part as crud_body_part,
    CRUDEquipment, equipment as crud_equipment
)

# 导入模块中已定义的实例
from app.crud.user import user as crud_user
# 删除错误的导入，因为app.crud.food中没有定义food实例
# from app.crud.food import food as crud_food
# exercise.py文件不存在，暂时注释掉
# from app.crud.exercise import exercise as crud_exercise

# LangChain相关模型导入
from app.models.conversation import Conversation
from app.models.message import Message
from app.models.qa_pair import QAPair

# 创建CRUD实例
from app.models.user import User
from app.models.user_setting import UserSetting
from app.models.share_track import ShareTrack
from app.models.food import Food
from app.models.meal import MealRecord, FoodItem, FoodItemNutrientIntake, HealthRecommendation, FoodRecognition

# 创建实例
user = CRUDUser(User)
user_setting = CRUDUserSetting(UserSetting)
# 为了兼容性，同时导出为crud_user_setting
crud_user_setting = user_setting
share_track = CRUDShareTrack(ShareTrack)
food = CRUDFood(Food)
meal = CRUDMealRecord(MealRecord)
food_item = CRUDFoodItem(FoodItem)
food_item_nutrient_intake = CRUDFoodItemNutrientIntake(FoodItemNutrientIntake)
health_recommendation = CRUDHealthRecommendation(HealthRecommendation)
food_recognition = CRUDFoodRecognition(FoodRecognition)

# LangChain相关实例
from app.models.conversation import Conversation
from app.models.message import Message
from app.models.qa_pair import QAPair

# 创建额外的CRUD实例（如果没有在导入时定义）
if 'crud_conversation' not in locals():
    crud_conversation = CRUDConversation(Conversation)
if 'crud_message' not in locals():
    crud_message = CRUDMessage(Message)
if 'crud_qa_pair' not in locals():
    crud_qa_pair = CRUDQAPair(QAPair)

# 导入社区相关CRUD
from app.crud.crud_daily_workout import CRUDDailyWorkout, daily_workout as crud_daily_workout
from app.crud.crud_community import (
    CRUDPost, post as crud_post,
    CRUDComment, comment as crud_comment,
    CRUDPostLike, post_like as crud_post_like,
    CRUDCommentLike, comment_like as crud_comment_like,
    CRUDReport, report as crud_report,
    CRUDNotification, notification as crud_notification
)

# 导入游戏化模块CRUD
from app.crud.gamification import (
    # Level related
    user_level,
    user_attribute,
    user_title,
    
    # Card related
    card,
    user_card,
    card_synthesis_recipe,
    
    # Currency related
    currency,
    currency_transaction,
    shop_item,
    user_purchase,
    
    # Achievement related
    achievement,
    user_achievement,
    milestone,
    user_milestone,
    
    # Task related
    task,
    user_task,
    daily_checkin
)