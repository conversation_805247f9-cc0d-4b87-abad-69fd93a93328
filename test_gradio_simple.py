#!/usr/bin/env python3
"""
简化的Gradio测试系统验证脚本

快速验证修复是否成功，避免复杂的依赖问题
"""

import os
import sys
import asyncio
from pathlib import Path

# 设置测试模式
os.environ['TEST_MODE'] = 'true'

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_ai_assistant_tester():
    """测试AIAssistantTester是否正常工作"""
    try:
        # 导入测试器
        sys.path.insert(0, str(project_root / "tests" / "comprehensive" / "integration"))
        from gradio_ai_assistant_tester import AIAssistantTester, TEST_MODE
        
        print(f"✅ 成功导入AIAssistantTester，测试模式: {TEST_MODE}")
        
        # 创建测试器实例
        tester = AIAssistantTester()
        print("✅ 成功创建AIAssistantTester实例")
        
        # 测试发送消息
        result = await tester.send_message_to_ai("你好，我想开始健身")
        print(f"✅ 成功发送测试消息，收到回复: {result['response'][:50]}...")
        
        # 测试性能指标
        print(f"✅ 响应时间: {result['response_time']:.2f}ms")
        print(f"✅ 意图识别: {result['intent']}")
        print(f"✅ 置信度: {result['confidence']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_gradio_import():
    """测试Gradio导入"""
    try:
        import gradio as gr
        print(f"✅ Gradio导入成功 (版本: {gr.__version__})")
        return True
    except ImportError as e:
        print(f"❌ Gradio导入失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 简化的Gradio测试系统验证")
    print("=" * 60)
    
    # 测试项目
    tests = [
        ("Gradio导入", test_gradio_import),
        ("AI助手测试器", test_ai_assistant_tester),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<30} {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！Gradio测试系统修复成功！")
        print("\n📝 现在可以运行完整的测试系统:")
        print("   source .venv/bin/activate")
        print("   export TEST_MODE=true")
        print("   python run_gradio_tester.py")
        print("   访问 http://localhost:7860")
    else:
        print(f"\n⚠️  还有 {total - passed} 项测试未通过。")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main()) 