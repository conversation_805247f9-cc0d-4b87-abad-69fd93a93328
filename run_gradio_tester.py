#!/usr/bin/env python3
"""
智能健身AI助手系统 - Gradio测试平台启动脚本

运行说明:
source .venv/bin/activate && python run_gradio_tester.py

访问地址:
http://localhost:7860
"""

import sys
import os
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_virtual_env():
    """检查是否在虚拟环境中"""
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.warning("⚠️  建议在虚拟环境中运行: source .venv/bin/activate")
    else:
        logger.info("✅ 运行在虚拟环境中")

def check_dependencies():
    """检查依赖环境"""
    try:
        import gradio
        logger.info(f"✅ Gradio版本: {gradio.__version__}")
    except ImportError:
        logger.error("❌ 缺少Gradio依赖，请安装: pip install gradio")
        return False
    
    try:
        import fastapi
        logger.info(f"✅ FastAPI可用")
    except ImportError:
        logger.error("❌ 缺少FastAPI依赖")
        return False
    
    return True

def main():
    """启动Gradio测试平台"""
    logger.info("🚀 启动智能健身AI助手系统测试平台...")
    
    # 检查虚拟环境
    check_virtual_env()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查测试文件是否存在
    test_file = "tests/comprehensive/integration/gradio_ai_assistant_tester.py"
    if not os.path.exists(test_file):
        logger.error(f"❌ 测试文件不存在: {test_file}")
        sys.exit(1)
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        if 'TEST_MODE' not in env:
            env['TEST_MODE'] = 'true'
        
        # 设置端口（如果未设置）
        if 'GRADIO_SERVER_PORT' not in env:
            env['GRADIO_SERVER_PORT'] = '7860'
        
        # 运行测试平台
        logger.info("📡 启动测试平台...")
        logger.info(f"🌐 访问地址: http://localhost:{env.get('GRADIO_SERVER_PORT', '7860')}")
        logger.info("📊 测试配置:")
        logger.info("   - 用户ID: 1")
        logger.info("   - 微信OpenID: oCU0j7Rg9kzigLzquCBje3KfnQXk")
        logger.info("   - API端点: v2/endpoints/chat.py")
        
        # 方式1: 直接导入并运行（推荐）
        try:
            # 添加项目根目录到Python路径
            sys.path.insert(0, os.path.abspath('.'))
            
            # 直接导入并运行主函数
            import tests.comprehensive.integration.gradio_ai_assistant_tester as tester_module
            tester_module.main()
            
        except ImportError as e:
            logger.warning(f"⚠️ 直接导入失败: {e}")
            logger.info("🔄 尝试使用子进程方式...")
            
            # 方式2: 使用子进程（备用）
            subprocess.run([sys.executable, test_file], env=env, check=True)
        
    except KeyboardInterrupt:
        logger.info("👋 测试平台已停止")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 测试平台启动失败: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 