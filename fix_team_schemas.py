#!/usr/bin/env python3
"""
批量修复team schemas文件格式问题的脚本
主要修复class定义被拼接在一起的问题
"""

import os
import re

def fix_schema_file(file_path):
    """修复单个schema文件的格式问题"""
    print(f"正在修复: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 修复 orm_mode = True 相关问题
    content = re.sub(r'orm_mode = True.*?class ', 'from_attributes = True\n\n\nclass ', content, flags=re.DOTALL)
    
    # 2. 修复被拼接的class定义
    content = re.sub(r'(from_attributes = True)\s*#.*?class ', r'\1\n\n\n# 新类定义\nclass ', content, flags=re.DOTALL)
    
    # 3. 修复重复的 from_attributes = True 注释
    content = re.sub(r'from_attributes = True\s*#[^c]*', 'from_attributes = True', content)
    
    # 4. 确保class前有适当的换行和注释
    content = re.sub(r'(class \w+\(BaseModel\):)', r'# \1的定义\n\1', content)
    
    # 5. 删除重复的Config类定义
    content = re.sub(r'(class Config:\s+from_attributes = True)\s+\1+', r'\1', content)
    
    # 6. 确保文件结尾正确
    if not content.endswith('\n'):
        content += '\n'
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"修复完成: {file_path}")

def main():
    """主函数"""
    team_schema_dir = "app/schemas/team"
    
    # 获取所有.py文件（除了__init__.py）
    schema_files = []
    for file in os.listdir(team_schema_dir):
        if file.endswith('.py') and file != '__init__.py':
            schema_files.append(os.path.join(team_schema_dir, file))
    
    print(f"找到 {len(schema_files)} 个需要修复的文件")
    
    for file_path in schema_files:
        try:
            fix_schema_file(file_path)
        except Exception as e:
            print(f"修复 {file_path} 时出错: {str(e)}")
    
    print("所有文件修复完成!")

if __name__ == "__main__":
    main() 