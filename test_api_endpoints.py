"""
通过HTTP请求测试训练模板应用API端点
"""

import requests
import json
from datetime import datetime, timedelta

# API配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_api_health():
    """测试API健康状况"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务正常运行")
            return True
        else:
            print(f"⚠️ API响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API连接失败: {str(e)}")
        return False

def test_docs_access():
    """测试API文档访问"""
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档可以正常访问")
            return True
        else:
            print(f"⚠️ API文档访问异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API文档访问失败: {str(e)}")
        return False

def test_get_training_templates():
    """测试获取训练模板列表"""
    try:
        # 注意：这里需要认证，我们先测试不需要认证的端点
        response = requests.get(f"{API_BASE}/workout-templates/", timeout=10)
        print(f"📝 获取训练模板 - 状态码: {response.status_code}")
        if response.status_code == 401:
            print("ℹ️ 需要认证，这是正常的")
            return True
        elif response.status_code == 200:
            data = response.json()
            print(f"📊 获取到 {len(data)} 个训练模板")
            return True
        else:
            print(f"⚠️ 响应异常: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {str(e)}")
        return False

def test_apply_template_endpoint():
    """测试应用训练模板端点结构"""
    try:
        # 测试POST请求到apply端点（预期会因为认证或参数问题而失败，但可以验证端点存在）
        template_id = 1  # 假设的模板ID
        response = requests.post(
            f"{API_BASE}/workout-templates/{template_id}/apply",
            json={"date": "2024-12-20"},
            timeout=10
        )
        print(f"📝 应用训练模板端点 - 状态码: {response.status_code}")
        if response.status_code == 401:
            print("ℹ️ 需要认证，端点存在且正常")
            return True
        elif response.status_code == 404:
            print("ℹ️ 模板不存在或端点路由正常")
            return True
        else:
            print(f"📋 响应内容: {response.text}")
            return True
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {str(e)}")
        return False

def test_openapi_schema():
    """测试OpenAPI模式，查看是否包含新的端点"""
    try:
        response = requests.get(f"{BASE_URL}/openapi.json", timeout=10)
        if response.status_code == 200:
            schema = response.json()
            
            # 检查是否有我们新添加的端点
            paths = schema.get("paths", {})
            apply_endpoint = "/api/v1/workout-templates/{template_id}/apply"
            workouts_endpoint = "/api/v1/workout-templates/{template_id}/workouts"
            
            if apply_endpoint in paths:
                print("✅ apply_workout_template 端点已注册")
                apply_methods = list(paths[apply_endpoint].keys())
                print(f"   支持的HTTP方法: {apply_methods}")
            else:
                print("❌ apply_workout_template 端点未找到")
            
            if workouts_endpoint in paths:
                print("✅ get_workouts_from_template 端点已注册")
                workouts_methods = list(paths[workouts_endpoint].keys())
                print(f"   支持的HTTP方法: {workouts_methods}")
            else:
                print("❌ get_workouts_from_template 端点未找到")
                
            return True
        else:
            print(f"⚠️ OpenAPI schema访问异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ OpenAPI schema访问失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 开始API端点测试\n")
    
    # 测试API基础功能
    print("1. 测试API健康状况...")
    test_api_health()
    
    print("\n2. 测试API文档访问...")
    test_docs_access()
    
    print("\n3. 测试OpenAPI模式...")
    test_openapi_schema()
    
    print("\n4. 测试训练模板端点...")
    test_get_training_templates()
    
    print("\n5. 测试应用模板端点...")
    test_apply_template_endpoint()
    
    print("\n🎯 API测试完成!")
    print("\n💡 提示：要完整测试功能，需要:")
    print("   1. 有效的认证token")
    print("   2. 数据库中存在的训练模板")
    print("   3. 有效的用户账户") 