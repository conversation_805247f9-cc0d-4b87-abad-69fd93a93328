#!/usr/bin/env python3
"""
增强版Gradio聊天测试系统

修复了API集成问题，确保能正确调用chat.py API
"""

import gradio as gr
import requests
import json
import time
import asyncio
from typing import Dict, Any, List, Optional

# API配置
API_BASE_URL = "http://localhost:8000/api/v2/chat"
DEFAULT_SESSION_ID = "gradio-test-session"

# 认证Token（需要先登录获取）
AUTH_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMCIsImV4cCI6MTc1MTM4MDU4N30.fglVh7Fut2qrj1eo-6ftWyUYkdSIB9JniUKZt3Kzzuk"

class GradioAPIClient:
    """API客户端类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {AUTH_TOKEN}",
            "Content-Type": "application/json"
        })
        self.current_session_id = DEFAULT_SESSION_ID
        
    def send_message(self, message: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """发送消息到API"""
        if not session_id:
            session_id = self.current_session_id
            
        # 使用v2 API的message端点
        url = f"{API_BASE_URL}/message"
        
        payload = {
            "message": message,
            "session_id": session_id,
            "meta_info": {}
        }
        
        try:
            print(f"🚀 发送API请求: {url}")
            print(f"📝 请求数据: {json.dumps(payload, ensure_ascii=False)}")
            
            response = self.session.post(url, json=payload, timeout=30)
            
            print(f"📡 响应状态码: {response.status_code}")
            print(f"📋 响应内容: {response.text[:500]}...")
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "response": result.get("response", ""),
                    "intent": result.get("intent_type", "unknown"),
                    "confidence": result.get("confidence", 0.0),
                    "source_system": result.get("meta_info", {}).get("source_system", "unknown"),
                    "conversation_id": result.get("conversation_id", session_id)
                }
            else:
                return {
                    "success": False,
                    "error": f"API错误: {response.status_code} - {response.text}",
                    "response": f"请求失败: {response.status_code}"
                }
                
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "error": f"请求异常: {str(e)}",
                "response": f"网络错误: {str(e)}"
            }
    
    def get_conversation_history(self, session_id: Optional[str] = None) -> List[Dict]:
        """获取对话历史"""
        if not session_id:
            session_id = self.current_session_id
            
        url = f"{API_BASE_URL}/sessions/{session_id}/messages"
        
        try:
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return data.get("messages", [])
            else:
                print(f"获取对话历史失败: {response.status_code}")
                return []
        except Exception as e:
            print(f"获取对话历史异常: {str(e)}")
            return []

# 全局API客户端
api_client = GradioAPIClient()

def format_chat_history(history: List[List[str]]) -> List[List[str]]:
    """格式化聊天历史"""
    formatted_history = []
    for item in history:
        if len(item) >= 2:
            formatted_history.append([item[0], item[1]])
    return formatted_history

def chat_with_ai(message: str, history: List[List[str]]) -> tuple:
    """与AI聊天的主函数"""
    if not message.strip():
        return history, ""
    
    print(f"\n🔥 开始处理用户消息: {message}")
    
    # 添加用户消息到历史
    history = history or []
    history.append([message, "正在思考中..."])
    
    # 调用API
    result = api_client.send_message(message)
    
    if result["success"]:
        ai_response = result["response"]
        intent = result.get("intent", "unknown")
        confidence = result.get("confidence", 0.0)
        source_system = result.get("source_system", "unknown")
        
        # 构建详细的回复信息
        detailed_response = f"""**AI回复**: {ai_response}

**处理信息**:
- 意图识别: {intent} (置信度: {confidence:.2f})
- 处理系统: {source_system}
- 会话ID: {result.get('conversation_id', 'unknown')}
"""
        
        # 更新历史中的AI回复
        history[-1][1] = detailed_response
        
        print(f"✅ AI回复成功: {ai_response[:100]}...")
        print(f"📊 意图: {intent}, 置信度: {confidence:.2f}, 系统: {source_system}")
        
    else:
        error_message = f"❌ 处理失败: {result.get('error', '未知错误')}"
        history[-1][1] = error_message
        print(f"❌ 处理失败: {result.get('error', '未知错误')}")
    
    return history, ""

def test_integration_manager():
    """测试集成管理器功能"""
    print("🧪 开始测试集成管理器...")
    
    test_cases = [
        {"message": "我想制定一个训练计划", "expected_intent": "training_plan"},
        {"message": "胸肌怎么练", "expected_intent": "exercise_action"},
        {"message": "给我一些健身建议", "expected_intent": "fitness_advice"},
        {"message": "饮食方面有什么建议", "expected_intent": "diet_advice"},
        {"message": "你好", "expected_intent": "general_chat"}
    ]
    
    results = []
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['message']}")
        result = api_client.send_message(test_case["message"])
        
        if result["success"]:
            intent = result.get("intent", "unknown")
            expected = test_case["expected_intent"]
            success = intent == expected or (expected in ["exercise_action", "fitness_advice"] and intent in ["exercise_action", "fitness_advice"])
            
            status = "✅ 通过" if success else "❌ 失败"
            print(f"   {status} - 意图: {intent} (期望: {expected})")
            print(f"   回复: {result['response'][:80]}...")
            
            results.append({
                "test_case": i,
                "message": test_case["message"],
                "expected_intent": expected,
                "actual_intent": intent,
                "success": success,
                "response": result["response"]
            })
        else:
            print(f"   ❌ API调用失败: {result.get('error', '未知错误')}")
            results.append({
                "test_case": i,
                "message": test_case["message"],
                "expected_intent": test_case["expected_intent"],
                "actual_intent": "error",
                "success": False,
                "response": result.get("error", "API调用失败")
            })
    
    # 生成测试报告
    passed = sum(1 for r in results if r["success"])
    total = len(results)
    
    report = f"""
🧪 集成管理器测试报告
========================
总测试用例: {total}
通过: {passed}
失败: {total - passed}
成功率: {passed/total*100:.1f}%

详细结果:
"""
    
    for result in results:
        status = "✅" if result["success"] else "❌"
        report += f"""
{status} 测试用例 {result['test_case']}:
   消息: {result['message']}
   期望意图: {result['expected_intent']}
   实际意图: {result['actual_intent']}
   回复: {result['response'][:100]}...
"""
    
    return report

def clear_conversation():
    """清空对话"""
    # 更新会话ID以开始新对话
    api_client.current_session_id = f"gradio-test-session-{int(time.time())}"
    return [], f"对话已清空。新会话ID: {api_client.current_session_id}"

def show_conversation_info():
    """显示对话信息"""
    history = api_client.get_conversation_history()
    info = f"""
**当前对话信息**:
- 会话ID: {api_client.current_session_id}
- 消息数量: {len(history)}
- API地址: {API_BASE_URL}
"""
    return info

# Gradio界面
def create_gradio_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(title="智能健身AI助手测试系统", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🏋️ 智能健身AI助手测试系统")
        gr.Markdown("### 增强版 - 集成管理器功能测试")
        
        with gr.Tab("💬 AI对话测试"):
            chatbot = gr.Chatbot(
                label="AI助手对话",
                height=500,
                show_label=True,
                container=True,
                show_copy_button=True
            )
            
            with gr.Row():
                msg = gr.Textbox(
                    placeholder="请输入您的健身问题...",
                    show_label=False,
                    scale=4
                )
                send_btn = gr.Button("发送", variant="primary", scale=1)
                clear_btn = gr.Button("清空对话", variant="secondary", scale=1)
            
            with gr.Row():
                info_display = gr.Markdown("等待开始对话...")
            
            # 事件绑定
            msg.submit(
                fn=chat_with_ai,
                inputs=[msg, chatbot],
                outputs=[chatbot, msg]
            ).then(
                fn=show_conversation_info,
                outputs=info_display
            )
            
            send_btn.click(
                fn=chat_with_ai,
                inputs=[msg, chatbot],
                outputs=[chatbot, msg]
            ).then(
                fn=show_conversation_info,
                outputs=info_display
            )
            
            clear_btn.click(
                fn=clear_conversation,
                outputs=[chatbot, info_display]
            )
        
        with gr.Tab("🧪 集成管理器测试"):
            gr.Markdown("### 自动化测试集成管理器功能")
            
            test_btn = gr.Button("开始测试", variant="primary", size="lg")
            test_output = gr.Markdown("点击开始测试按钮运行测试...")
            
            test_btn.click(
                fn=test_integration_manager,
                outputs=test_output
            )
        
        with gr.Tab("📊 系统状态"):
            gr.Markdown("### 系统配置和状态信息")
            
            status_info = gr.Markdown(f"""
**API配置**:
- 基础URL: {API_BASE_URL}
- 当前会话ID: {api_client.current_session_id}
- 认证状态: {"✅ 已配置" if AUTH_TOKEN else "❌ 未配置"}

**功能特性**:
- ✅ 集成管理器路由
- ✅ 意图识别和置信度显示
- ✅ 多系统处理信息
- ✅ 详细响应格式化
- ✅ 错误处理和回退机制

**测试建议**:
1. 尝试发送: "我想制定一个训练计划"
2. 尝试发送: "胸肌怎么练"
3. 尝试发送: "给我一些健身建议"
4. 观察不同意图的路由和处理结果
""")
        
        with gr.Tab("🔧 快速测试"):
            gr.Markdown("### 常用测试用例")
            
            quick_tests = [
                "我想制定一个训练计划",
                "胸肌怎么练",
                "腹肌训练有什么好方法",
                "给我一些健身建议",
                "饮食方面有什么建议",
                "如何提高跑步速度",
                "你好",
                "感谢"
            ]
            
            for test_msg in quick_tests:
                quick_btn = gr.Button(f"测试: {test_msg}", size="sm")
                quick_btn.click(
                    fn=lambda msg=test_msg: chat_with_ai(msg, []),
                    inputs=[],
                    outputs=[chatbot, msg]
                )
    
    return demo

if __name__ == "__main__":
    print("🚀 启动增强版Gradio聊天测试系统...")
    print(f"🔗 API地址: {API_BASE_URL}")
    print(f"🔑 会话ID: {api_client.current_session_id}")
    
    # 创建界面
    demo = create_gradio_interface()
    
    # 启动服务
    demo.launch(
        server_name="0.0.0.0",
        server_port=7862,
        share=False,
        show_api=False,
        inbrowser=True
    ) 