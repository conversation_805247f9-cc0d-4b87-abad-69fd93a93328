#!/usr/bin/env python3
"""
Pydantic V2兼容性修复脚本

自动修复项目中的Pydantic V1到V2兼容性问题：
1. 将 orm_mode = True 替换为 from_attributes = True
2. 将 schema_extra 替换为 json_schema_extra
"""

import os
import re
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PydanticV2Fixer:
    """Pydantic V2兼容性修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.changes_made = 0
        
    def fix_orm_mode(self, content: str) -> tuple[str, bool]:
        """
        修复orm_mode配置
        将 orm_mode = True 替换为 from_attributes = True
        """
        original_content = content
        
        # 更精确的匹配模式，处理不同的空白符和注释
        patterns = [
            # 匹配 orm_mode = True (可能有空白符)
            r'(\s+)orm_mode\s*=\s*True\s*(?:#.*)?$',
            # 匹配可能的其他形式
            r'(\s+)orm_mode\s*=\s*True\s*(?:\n|\r\n?)'
        ]
        
        replacement = r'\1from_attributes = True  # 替换了 orm_mode = True'
        
        for pattern in patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        return content, content != original_content
    
    def fix_schema_extra(self, content: str) -> tuple[str, bool]:
        """
        修复schema_extra配置
        将 schema_extra 替换为 json_schema_extra
        """
        original_content = content
        
        # 更精确的匹配模式
        patterns = [
            # 匹配 schema_extra = {
            r'(\s+)schema_extra\s*=\s*\{',
            # 匹配其他可能的schema_extra格式
            r'(\s+)schema_extra\s*=\s*'
        ]
        
        for pattern in patterns:
            if 'schema_extra\s*=\s*\{' in pattern:
                replacement = r'\1json_schema_extra = {'
            else:
                replacement = r'\1json_schema_extra = '
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        return content, content != original_content
    
    def fix_file(self, file_path: Path) -> bool:
        """修复单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_changed = False
            
            # 修复orm_mode
            content, orm_changed = self.fix_orm_mode(content)
            if orm_changed:
                file_changed = True
                logger.info(f"修复 orm_mode: {file_path}")
            
            # 修复schema_extra
            content, schema_changed = self.fix_schema_extra(content)
            if schema_changed:
                file_changed = True
                logger.info(f"修复 schema_extra: {file_path}")
            
            # 如果文件有变化，写回文件
            if file_changed:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.changes_made += 1
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"修复文件失败 {file_path}: {e}")
            return False
    
    def fix_schemas_directory(self):
        """修复schemas目录下的所有Python文件"""
        schemas_dir = self.project_root / "app" / "schemas"
        
        if not schemas_dir.exists():
            logger.error(f"schemas目录不存在: {schemas_dir}")
            return
        
        logger.info(f"开始修复schemas目录: {schemas_dir}")
        
        # 递归查找所有Python文件
        python_files = list(schemas_dir.rglob("*.py"))
        
        logger.info(f"找到 {len(python_files)} 个Python文件")
        
        for file_path in python_files:
            if file_path.name == "__init__.py":
                continue
                
            logger.debug(f"检查文件: {file_path}")
            if self.fix_file(file_path):
                logger.info(f"✅ 修复完成: {file_path}")
    
    def fix_all(self):
        """修复整个项目"""
        logger.info("🚀 开始Pydantic V2兼容性修复...")
        
        # 修复schemas目录
        self.fix_schemas_directory()
        
        # 也可以扩展到其他目录
        other_dirs = ["app/models", "tests"]
        for dir_name in other_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                logger.info(f"检查目录: {dir_path}")
                python_files = list(dir_path.rglob("*.py"))
                for file_path in python_files:
                    if self.fix_file(file_path):
                        logger.info(f"✅ 修复完成: {file_path}")
        
        logger.info(f"🎉 修复完成！共修复 {self.changes_made} 个文件")

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 Pydantic V2兼容性修复脚本")
    print("=" * 60)
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent  # 假设脚本在scripts目录下
    
    # 创建修复器
    fixer = PydanticV2Fixer(project_root)
    
    # 执行修复
    fixer.fix_all()
    
    print("=" * 60)
    print("✅ 修复完成！请重新运行测试系统")
    print("=" * 60)

if __name__ == "__main__":
    main() 