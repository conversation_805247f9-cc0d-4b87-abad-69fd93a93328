#!/usr/bin/env python3
"""
修复验证脚本

验证Gradio测试系统的所有修复是否正确应用：
1. Pydantic V2兼容性修复
2. ConversationOrchestrator参数调用修复
3. 测试模式功能验证
"""

import sys
import os
import importlib
import traceback
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_pydantic_compatibility():
    """检查Pydantic V2兼容性修复"""
    print("🔍 检查Pydantic V2兼容性...")
    
    # 检查一些关键的schema文件
    schema_files_to_check = [
        "app/schemas/user_setting.py",
        "app/schemas/chat.py", 
        "app/schemas/community.py",
        "app/schemas/training_plan.py"
    ]
    
    issues_found = []
    
    for file_path in schema_files_to_check:
        full_path = project_root / file_path
        if not full_path.exists():
            continue
            
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否还有旧的Pydantic V1语法
        if 'orm_mode = True' in content:
            issues_found.append(f"{file_path}: 仍包含 orm_mode = True")
        
        if 'schema_extra = {' in content:
            issues_found.append(f"{file_path}: 仍包含 schema_extra")
    
    if issues_found:
        print("❌ 发现Pydantic兼容性问题:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ Pydantic V2兼容性检查通过")
        return True

def check_gradio_import():
    """检查Gradio导入"""
    print("🔍 检查Gradio导入...")
    
    try:
        import gradio as gr
        print(f"✅ Gradio导入成功 (版本: {gr.__version__})")
        return True
    except ImportError as e:
        print(f"❌ Gradio导入失败: {e}")
        return False

def check_orchestrator_import():
    """检查ConversationOrchestrator导入"""
    print("🔍 检查ConversationOrchestrator导入...")
    
    try:
        from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
        print("✅ ConversationOrchestrator导入成功")
        return True
    except ImportError as e:
        print(f"⚠️  ConversationOrchestrator导入失败: {e}")
        print("   这是正常的，测试模式会处理这种情况")
        return False

def check_test_mode_functionality():
    """检查测试模式功能"""
    print("🔍 检查测试模式功能...")
    
    try:
        # 设置测试模式环境变量
        os.environ['TEST_MODE'] = 'true'
        
        # 导入测试器类
        sys.path.insert(0, str(project_root / "tests" / "comprehensive" / "integration"))
        from gradio_ai_assistant_tester import AIAssistantTester, TEST_MODE
        
        if TEST_MODE:
            print("✅ 测试模式检测正常")
        else:
            print("❌ 测试模式检测失败")
            return False
        
        # 创建测试器实例
        tester = AIAssistantTester()
        print("✅ AIAssistantTester实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试模式功能检查失败: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        return False

def check_method_signature():
    """检查方法签名修复"""
    print("🔍 检查ConversationOrchestrator.process_message方法签名...")
    
    try:
        from app.services.ai_assistant.conversation.orchestrator import ConversationOrchestrator
        import inspect
        
        # 获取process_message方法的签名
        signature = inspect.signature(ConversationOrchestrator.process_message)
        params = list(signature.parameters.keys())
        
        expected_params = ['self', 'message', 'conversation_id', 'user_info']
        
        if params == expected_params:
            print("✅ process_message方法签名正确")
            return True
        else:
            print(f"❌ process_message方法签名不正确: {params}")
            print(f"   期望: {expected_params}")
            return False
            
    except ImportError:
        print("⚠️  无法导入ConversationOrchestrator，跳过签名检查")
        return True
    except Exception as e:
        print(f"❌ 方法签名检查失败: {e}")
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("🔧 Gradio测试系统修复验证")
    print("=" * 60)
    
    checks = [
        ("Pydantic V2兼容性", check_pydantic_compatibility),
        ("Gradio导入", check_gradio_import),
        ("ConversationOrchestrator导入", check_orchestrator_import),
        ("方法签名", check_method_signature),
        ("测试模式功能", check_test_mode_functionality),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n📋 执行检查: {check_name}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            results.append((check_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 验证结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 所有检查通过！系统修复成功。")
        print("\n📝 现在可以运行测试系统:")
        print("   export TEST_MODE=true")
        print("   python3 run_gradio_tester.py")
        print("   访问 http://localhost:7860")
    else:
        print(f"\n⚠️  还有 {total - passed} 项检查未通过，需要进一步修复。")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 