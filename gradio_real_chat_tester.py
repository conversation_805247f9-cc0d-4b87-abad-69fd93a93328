#!/usr/bin/env python3
"""
Gradio真实聊天测试系统
连接到后端真实API进行完整的聊天功能测试
"""

import gradio as gr
import requests
import json
import time
import threading
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealChatTester:
    """真实聊天测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v2"
        self.session_id = None
        self.access_token = None
        self.user_info = {}
        self.conversation_history = []
        
    def authenticate(self, username: str, password: str) -> tuple[bool, str]:
        """用户认证"""
        try:
            # 登录获取token
            login_data = {
                "username": username,
                "password": password
            }
            
            response = requests.post(
                f"{self.base_url}/api/v1/auth/login",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get("access_token")
                logger.info(f"认证成功，用户: {username}")
                
                # 获取用户信息
                user_response = requests.get(
                    f"{self.base_url}/api/v1/user/me",
                    headers={"Authorization": f"Bearer {self.access_token}"}
                )
                
                if user_response.status_code == 200:
                    self.user_info = user_response.json()
                    return True, f"认证成功！用户: {self.user_info.get('nickname', username)}"
                else:
                    return False, "获取用户信息失败"
            else:
                error_msg = response.json().get("detail", "认证失败")
                return False, f"认证失败: {error_msg}"
                
        except Exception as e:
            logger.error(f"认证过程出错: {str(e)}")
            return False, f"认证过程出错: {str(e)}"
    
    def create_new_session(self) -> str:
        """创建新的会话"""
        import uuid
        self.session_id = str(uuid.uuid4())
        self.conversation_history = []
        logger.info(f"创建新会话: {self.session_id}")
        return self.session_id
    
    def load_conversation_history(self, session_id: str) -> tuple[List[tuple], str]:
        """加载会话历史记录"""
        if not self.access_token:
            return [], "请先登录"
        
        try:
            # 获取会话消息历史
            response = requests.get(
                f"{self.api_base}/chat/sessions/{session_id}/messages",
                headers={"Authorization": f"Bearer {self.access_token}"},
                params={"limit": 50}
            )
            
            if response.status_code == 200:
                data = response.json()
                messages = data.get("messages", [])
                
                # 转换为Gradio聊天格式
                history = []
                for msg in reversed(messages):  # 反转顺序，最新的在下面
                    if msg["role"] == "user":
                        history.append([msg["content"], None])
                    elif msg["role"] == "assistant" and history:
                        history[-1][1] = msg["content"]
                    elif msg["role"] == "assistant":
                        history.append([None, msg["content"]])
                
                self.conversation_history = history
                self.session_id = session_id
                
                logger.info(f"加载会话历史成功，共 {len(messages)} 条消息")
                return history, f"加载成功，共 {len(messages)} 条消息"
            else:
                error_msg = response.json().get("detail", "加载失败")
                return [], f"加载会话历史失败: {error_msg}"
                
        except Exception as e:
            logger.error(f"加载会话历史出错: {str(e)}")
            return [], f"加载会话历史出错: {str(e)}"
    
    def send_message(self, message: str, history: List[tuple]) -> tuple[List[tuple], str, str]:
        """发送消息并获取回复"""
        if not self.access_token:
            return history, "", "请先登录"
        
        if not self.session_id:
            self.create_new_session()
        
        try:
            # 发送消息到后端
            chat_data = {
                "message": message,
                "session_id": self.session_id,
                "meta_info": {
                    "timestamp": datetime.now().isoformat(),
                    "gradio_test": True
                }
            }
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.api_base}/chat/message",
                json=chat_data,
                headers=headers,
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                response_data = response.json()
                ai_response = response_data.get("response", "")
                
                # 更新历史记录
                new_history = history + [[message, ai_response]]
                
                # 准备状态信息
                status_info = {
                    "会话ID": self.session_id,
                    "响应时间": f"{response_time:.2f}秒",
                    "意图类型": response_data.get("intent_type"),
                    "置信度": response_data.get("confidence"),
                    "成功状态": response_data.get("success", False)
                }
                
                status_text = "\n".join([f"{k}: {v}" for k, v in status_info.items() if v is not None])
                
                logger.info(f"消息发送成功，响应时间: {response_time:.2f}秒")
                return new_history, "", status_text
            else:
                error_msg = response.json().get("detail", "发送失败")
                error_text = f"发送消息失败: {error_msg}"
                logger.error(error_text)
                return history, "", error_text
                
        except Exception as e:
            error_text = f"发送消息出错: {str(e)}"
            logger.error(error_text)
            return history, "", error_text
    
    def get_conversation_list(self) -> str:
        """获取会话列表"""
        if not self.access_token:
            return "请先登录"
        
        try:
            response = requests.get(
                f"{self.api_base}/chat/conversations",
                headers={"Authorization": f"Bearer {self.access_token}"},
                params={"limit": 20}
            )
            
            if response.status_code == 200:
                data = response.json()
                conversations = data.get("conversations", [])
                
                if not conversations:
                    return "暂无会话记录"
                
                result = f"共找到 {len(conversations)} 个会话:\n\n"
                for conv in conversations:
                    result += f"会话ID: {conv.get('session_id', 'N/A')}\n"
                    result += f"创建时间: {conv.get('created_at', 'N/A')}\n"
                    result += f"最后活跃: {conv.get('last_active_at', 'N/A')}\n"
                    result += "-" * 50 + "\n"
                
                return result
            else:
                error_msg = response.json().get("detail", "获取失败")
                return f"获取会话列表失败: {error_msg}"
                
        except Exception as e:
            return f"获取会话列表出错: {str(e)}"
    
    def test_streaming_response(self, message: str) -> str:
        """测试流式响应（模拟）"""
        if not self.access_token:
            return "请先登录"
        
        # 这里可以实现WebSocket连接测试
        # 目前先返回模拟信息
        return f"流式响应测试 - 消息: {message}\n会话ID: {self.session_id}\n状态: 功能开发中..."

def create_gradio_interface():
    """创建Gradio界面"""
    
    # 创建测试器实例
    tester = RealChatTester()
    
    with gr.Blocks(title="健身助手聊天测试系统", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🏋️ 健身助手真实聊天测试系统")
        gr.Markdown("连接到后端真实API，测试完整的聊天功能")
        
        with gr.Tabs():
            # 认证标签页
            with gr.TabItem("🔐 用户认证"):
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### 登录测试")
                        username_input = gr.Textbox(
                            label="用户名",
                            placeholder="输入用户名",
                            value="<EMAIL>"
                        )
                        password_input = gr.Textbox(
                            label="密码",
                            type="password",
                            placeholder="输入密码"
                        )
                        login_btn = gr.Button("登录", variant="primary")
                        auth_status = gr.Textbox(
                            label="认证状态",
                            interactive=False,
                            lines=3
                        )
                    
                    with gr.Column():
                        gr.Markdown("### 用户信息")
                        user_info_display = gr.JSON(
                            label="当前用户信息",
                            value={}
                        )
            
            # 聊天测试标签页
            with gr.TabItem("💬 聊天测试"):
                with gr.Row():
                    with gr.Column(scale=3):
                        chatbot = gr.Chatbot(
                            label="聊天界面",
                            height=500,
                            show_label=True
                        )
                        
                        with gr.Row():
                            msg_input = gr.Textbox(
                                label="输入消息",
                                placeholder="输入您的消息...",
                                scale=4
                            )
                            send_btn = gr.Button("发送", variant="primary", scale=1)
                        
                        with gr.Row():
                            clear_btn = gr.Button("清空聊天")
                            new_session_btn = gr.Button("新会话")
                    
                    with gr.Column(scale=1):
                        gr.Markdown("### 会话控制")
                        session_id_input = gr.Textbox(
                            label="会话ID",
                            placeholder="输入会话ID加载历史"
                        )
                        load_history_btn = gr.Button("加载历史")
                        
                        gr.Markdown("### 响应状态")
                        response_status = gr.Textbox(
                            label="状态信息",
                            lines=8,
                            interactive=False
                        )
            
            # 会话管理标签页
            with gr.TabItem("📋 会话管理"):
                with gr.Row():
                    with gr.Column():
                        get_conversations_btn = gr.Button("获取会话列表", variant="primary")
                        conversations_display = gr.Textbox(
                            label="会话列表",
                            lines=15,
                            interactive=False
                        )
                    
                    with gr.Column():
                        gr.Markdown("### 流式响应测试")
                        stream_msg_input = gr.Textbox(
                            label="测试消息",
                            placeholder="输入测试消息"
                        )
                        stream_test_btn = gr.Button("测试流式响应")
                        stream_result = gr.Textbox(
                            label="流式响应结果",
                            lines=10,
                            interactive=False
                        )
            
            # 系统状态标签页
            with gr.TabItem("⚙️ 系统状态"):
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### API连接测试")
                        api_test_btn = gr.Button("测试API连接")
                        api_status = gr.Textbox(
                            label="API状态",
                            lines=10,
                            interactive=False
                        )
                    
                    with gr.Column():
                        gr.Markdown("### 统一架构状态")
                        arch_status_btn = gr.Button("获取架构状态")
                        arch_status_display = gr.JSON(
                            label="架构状态",
                            value={}
                        )
        
        # 事件处理函数
        def handle_login(username, password):
            success, message = tester.authenticate(username, password)
            if success:
                return message, tester.user_info
            else:
                return message, {}
        
        def handle_send_message(message, history):
            if not message.strip():
                return history, "", "请输入消息"
            return tester.send_message(message, history)
        
        def handle_load_history(session_id):
            if not session_id.strip():
                return [], "请输入会话ID"
            return tester.load_conversation_history(session_id)
        
        def handle_new_session():
            session_id = tester.create_new_session()
            return [], f"新会话已创建: {session_id}"
        
        def handle_clear_chat():
            return [], ""
        
        def test_api_connection():
            try:
                response = requests.get(f"{tester.base_url}/health", timeout=5)
                if response.status_code == 200:
                    return f"✅ API连接正常\n服务器响应: {response.json()}\n时间: {datetime.now()}"
                else:
                    return f"❌ API连接异常\n状态码: {response.status_code}\n时间: {datetime.now()}"
            except Exception as e:
                return f"❌ API连接失败\n错误: {str(e)}\n时间: {datetime.now()}"
        
        def get_arch_status():
            if not tester.access_token:
                return {}
            try:
                response = requests.get(
                    f"{tester.api_base}/chat/unified-architecture/status",
                    headers={"Authorization": f"Bearer {tester.access_token}"}
                )
                if response.status_code == 200:
                    return response.json().get("data", {})
                else:
                    return {"error": "获取状态失败"}
            except Exception as e:
                return {"error": str(e)}
        
        # 绑定事件
        login_btn.click(
            handle_login,
            inputs=[username_input, password_input],
            outputs=[auth_status, user_info_display]
        )
        
        send_btn.click(
            handle_send_message,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, msg_input, response_status]
        )
        
        msg_input.submit(
            handle_send_message,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, msg_input, response_status]
        )
        
        load_history_btn.click(
            handle_load_history,
            inputs=[session_id_input],
            outputs=[chatbot, response_status]
        )
        
        new_session_btn.click(
            handle_new_session,
            outputs=[chatbot, response_status]
        )
        
        clear_btn.click(
            handle_clear_chat,
            outputs=[chatbot, response_status]
        )
        
        get_conversations_btn.click(
            tester.get_conversation_list,
            outputs=[conversations_display]
        )
        
        stream_test_btn.click(
            tester.test_streaming_response,
            inputs=[stream_msg_input],
            outputs=[stream_result]
        )
        
        api_test_btn.click(
            test_api_connection,
            outputs=[api_status]
        )
        
        arch_status_btn.click(
            get_arch_status,
            outputs=[arch_status_display]
        )
    
    return demo

if __name__ == "__main__":
    # 检查依赖
    try:
        import gradio
        import requests
        print("✅ 所有依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install gradio requests")
        exit(1)
    
    # 启动Gradio应用
    print("🚀 启动健身助手聊天测试系统...")
    print("📝 请确保后端服务运行在 http://localhost:8000")
    print("🔗 Gradio界面将在浏览器中打开")
    
    demo = create_gradio_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    ) 